#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Login Dialog for فوترها (Fawterha)
Provides a login dialog for the Point of Sale system
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QMessageBox, QFormLayout, QDialogButtonBox,
    QGroupBox, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap

from database.pos_manager import POSManager
from models.user import User
from utils.translation_manager import tr


class POSLoginDialog(QDialog):
    """Dialog for POS login."""

    # Signal emitted when login is successful
    login_successful = Signal(User)

    def __init__(self, db_manager, parent=None):
        """Initialize the POS login dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.pos_manager = POSManager(db_manager)
        self.user = None

        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle(tr("pos.login_title", "تسجيل الدخول إلى نظام نقاط البيع"))
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setWindowIcon(QIcon("resources/icons/pos.png"))

        # Set layout direction based on current language
        from utils.translation_manager import get_translation_manager
        translation_manager = get_translation_manager()
        if translation_manager.current_language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Logo and title
        logo_layout = QHBoxLayout()
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # Logo
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/icons/logo.png")
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel(tr("pos.system_name", "نظام نقاط البيع"))
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #0d47a1;")
        title_label.setAlignment(Qt.AlignCenter)
        
        main_layout.addLayout(logo_layout)
        main_layout.addWidget(title_label)

        # Login form
        login_group = QGroupBox(tr("pos.login_credentials", "بيانات الدخول"))
        login_layout = QFormLayout(login_group)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)

        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(tr("pos.username_placeholder", "اسم المستخدم"))
        self.username_edit.setMinimumHeight(40)
        login_layout.addRow(tr("pos.username", "اسم المستخدم:"), self.username_edit)

        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText(tr("pos.password_placeholder", "كلمة المرور"))
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(40)
        login_layout.addRow(tr("pos.password", "كلمة المرور:"), self.password_edit)

        main_layout.addWidget(login_group)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(10)

        # Cancel button
        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Login button
        self.login_button = QPushButton(tr("pos.login", "تسجيل الدخول"))
        self.login_button.setMinimumHeight(40)
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.login)
        button_layout.addWidget(self.login_button)

        main_layout.addLayout(button_layout)

        # Set focus to username field
        self.username_edit.setFocus()

        # Connect enter key to login
        self.password_edit.returnPressed.connect(self.login)

    def login(self):
        """Attempt to login with the provided credentials."""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()

        if not username:
            QMessageBox.warning(
                self,
                tr("pos.login_error", "خطأ في تسجيل الدخول"),
                tr("pos.username_required", "يرجى إدخال اسم المستخدم")
            )
            self.username_edit.setFocus()
            return

        if not password:
            QMessageBox.warning(
                self,
                tr("pos.login_error", "خطأ في تسجيل الدخول"),
                tr("pos.password_required", "يرجى إدخال كلمة المرور")
            )
            self.password_edit.setFocus()
            return

        # Authenticate user
        user = self.pos_manager.authenticate_user(username, password)
        if not user:
            QMessageBox.warning(
                self,
                tr("pos.login_error", "خطأ في تسجيل الدخول"),
                tr("pos.invalid_credentials", "اسم المستخدم أو كلمة المرور غير صحيحة")
            )
            self.password_edit.clear()
            self.password_edit.setFocus()
            return

        # Check if user has POS access
        if not user.has_permission('create_pos_session'):
            QMessageBox.warning(
                self,
                tr("pos.login_error", "خطأ في تسجيل الدخول"),
                tr("pos.no_pos_access", "ليس لديك صلاحية للوصول إلى نظام نقاط البيع")
            )
            self.password_edit.clear()
            self.password_edit.setFocus()
            return

        # Login successful
        self.user = user
        self.login_successful.emit(user)
        self.accept()

    def get_user(self):
        """Get the authenticated user.

        Returns:
            User: Authenticated user or None if login failed
        """
        return self.user
