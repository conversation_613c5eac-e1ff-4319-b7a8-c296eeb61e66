#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory View for فوترها (Fawterha)
Provides UI for managing POS-specific inventory
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QLineEdit, QMessageBox, QSplitter, QFrame, QGroupBox,
    QCheckBox, QSpinBox, QDoubleSpinBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit,
    QInputDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from models.pos_inventory import POSInventory
from models.pos_inventory_transaction import POSInventoryTransaction
from models.product import Product
from utils.translation_manager import tr
from utils.formatting import format_currency, format_date

class POSInventoryView(QWidget):
    """POS Inventory view widget."""

    inventory_updated = Signal()
    language_changed = Signal(str)

    def __init__(self, db_manager, pos_manager, parent=None):
        """Initialize the POS inventory view.

        Args:
            db_manager: Database manager
            pos_manager: POS manager
            parent: Parent widget
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.pos_manager = pos_manager
        self.inventory_list = []
        self.selected_inventory_id = None

        # Connect to translation manager's language_changed signal
        from utils.translation_manager import get_translation_manager
        self.translation_manager = get_translation_manager()
        print(f"POS Inventory View: Connecting to translation manager...")
        self.translation_manager.language_changed.connect(self.on_language_changed)
        print(f"POS Inventory View: Connected to language_changed signal successfully")

        self.init_ui()
        self.load_data()

        # Apply initial language settings
        self.apply_language_settings()

    def apply_language_settings(self):
        """Apply language-specific settings and translations."""
        current_lang = self.translation_manager.current_language
        print(f"POS Inventory View: Applying language settings for: {current_lang}")

        # Set layout direction
        if current_lang == 'ar':
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        # Update all translations
        self.update_all_translations()

    def on_language_changed(self, language):
        """Handle language change signal."""
        print(f"POS Inventory View: Language changed to: {language}")
        self.apply_language_settings()

    def update_all_translations(self):
        """Update all UI element translations."""
        # Update title
        self.title_label.setText(tr("pos.pos_inventory_management", "إدارة مخزون نقاط البيع"))

        # Update search placeholder
        self.search_edit.setPlaceholderText(tr("pos.search_products_placeholder", "بحث عن منتجات..."))

        # Update checkbox
        self.show_zero_stock.setText(tr("pos.show_zero_stock", "إظهار المنتجات بدون مخزون"))

        # Update buttons
        self.add_product_button.setText(tr("pos.add_product_button", "إضافة منتج"))
        self.sync_button.setText(tr("pos.sync_from_main_button", "مزامنة من المخزون الرئيسي"))

        # Update table headers
        if hasattr(self, 'inventory_table'):
            headers = [
                tr("pos.product_column", "المنتج"),
                tr("pos.description_column", "الوصف"),
                tr("pos.barcode_column", "الباركود"),
                tr("pos.stock_column", "المخزون"),
                tr("pos.min_stock_column", "الحد الأدنى"),
                tr("pos.actions_column", "الإجراءات")
            ]
            self.inventory_table.setHorizontalHeaderLabels(headers)

        # Update category combo
        if hasattr(self, 'category_combo'):
            self.load_categories()

        # Reload data to update content
        self.load_data()

    def init_ui(self):
        """Initialize the UI with modern, professional design."""
        # Main layout with consistent spacing
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(24, 24, 24, 24)

        # Apply modern main widget styling
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 14px;
                color: #212529;
            }
        """)

        # Modern header section
        header_container = QFrame()
        header_container.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin-bottom: 8px;
            }
        """)
        header_layout = QVBoxLayout(header_container)
        header_layout.setSpacing(16)
        header_layout.setContentsMargins(24, 20, 24, 20)

        # Modern title design
        title_container = QHBoxLayout()

        self.title_label = QLabel(tr("pos.pos_inventory_management", "إدارة مخزون نقاط البيع"))
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: 600;
                color: #1a365d;
                padding: 0px;
                margin: 0px;
                background: transparent;
            }
        """)

        # Add title to container
        title_container.addWidget(self.title_label)
        title_container.addStretch()

        header_layout.addLayout(title_container)
        main_layout.addWidget(header_container)

        # Modern controls section
        controls_container = QFrame()
        controls_container.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin-bottom: 8px;
            }
        """)
        controls_layout = QVBoxLayout(controls_container)
        controls_layout.setSpacing(16)
        controls_layout.setContentsMargins(24, 20, 24, 20)

        # Search and filters row
        search_row = QHBoxLayout()
        search_row.setSpacing(16)

        # Modern search field
        search_label = QLabel(tr("pos.search_label", "البحث:"))
        search_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                color: #495057;
                margin-right: 8px;
            }
        """)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("pos.search_products_placeholder", "بحث عن منتجات..."))
        self.search_edit.setMinimumHeight(40)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px 16px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                font-size: 14px;
                color: #495057;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #0d6efd;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #adb5bd;
            }
            QLineEdit::placeholder {
                color: #6c757d;
            }
        """)
        self.search_edit.textChanged.connect(self.filter_inventory)

        search_row.addWidget(search_label)
        search_row.addWidget(self.search_edit)

        # Modern category filter
        category_label = QLabel(tr("pos.category_label", "الفئة:"))
        category_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                color: #495057;
                margin-right: 8px;
            }
        """)

        self.category_combo = QComboBox()
        self.category_combo.setMinimumHeight(40)
        self.category_combo.setStyleSheet("""
            QComboBox {
                padding: 10px 16px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                font-size: 14px;
                color: #495057;
                min-width: 180px;
            }
            QComboBox:focus {
                border-color: #0d6efd;
                outline: none;
            }
            QComboBox:hover {
                border-color: #adb5bd;
            }
            QComboBox::drop-down {
                border: none;
                width: 32px;
                background-color: #f8f9fa;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                selection-background-color: #e7f3ff;
                selection-color: #495057;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 16px;
                border-radius: 4px;
                margin: 2px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f8f9fa;
                color: #495057;
            }
        """)
        self.category_combo.currentIndexChanged.connect(self.filter_inventory)

        search_row.addWidget(category_label)
        search_row.addWidget(self.category_combo)

        # Add stretch to push checkbox to the right
        search_row.addStretch()

        # Modern checkbox
        self.show_zero_stock = QCheckBox(tr("pos.show_zero_stock", "إظهار المنتجات بدون مخزون"))
        self.show_zero_stock.setChecked(True)
        self.show_zero_stock.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                color: #495057;
                spacing: 12px;
                padding: 8px 16px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QCheckBox:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #6c757d;
                background-color: #ffffff;
            }
            QCheckBox::indicator:unchecked:hover {
                border-color: #495057;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #0d6efd;
                background-color: #0d6efd;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #0b5ed7;
                border-color: #0b5ed7;
            }
        """)
        self.show_zero_stock.stateChanged.connect(self.filter_inventory)
        search_row.addWidget(self.show_zero_stock)

        # Add search row to controls layout
        controls_layout.addLayout(search_row)

        # Modern action buttons row
        buttons_row = QHBoxLayout()
        buttons_row.setSpacing(12)

        # Add stretch to push buttons to the right
        buttons_row.addStretch()

        # Modern add product button
        self.add_product_button = QPushButton(tr("pos.add_product_button", "إضافة منتج"))
        self.add_product_button.setMinimumHeight(44)
        self.add_product_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: #ffffff;
                border: 2px solid #198754;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                text-align: center;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #157347;
                border-color: #157347;
            }
            QPushButton:pressed {
                background-color: #146c43;
                border-color: #146c43;
            }
        """)
        self.add_product_button.clicked.connect(self.add_new_product)
        buttons_row.addWidget(self.add_product_button)

        # Modern sync button
        self.sync_button = QPushButton(tr("pos.sync_from_main_button", "مزامنة من المخزون الرئيسي"))
        self.sync_button.setMinimumHeight(44)
        self.sync_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                border: 2px solid #0d6efd;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                text-align: center;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
                border-color: #0b5ed7;
            }
            QPushButton:pressed {
                background-color: #0a58ca;
                border-color: #0a58ca;
            }
        """)
        self.sync_button.clicked.connect(self.sync_from_main)
        buttons_row.addWidget(self.sync_button)

        # Add buttons row to controls layout
        controls_layout.addLayout(buttons_row)

        # Add controls container to main layout
        main_layout.addWidget(controls_container)

        # Remove the duplicate header layout addition
        # main_layout.addLayout(header_layout)  # This was already added above

        # Modern content area with splitter
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)
        content_splitter.setHandleWidth(6)
        content_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #dee2e6;
                border-radius: 3px;
                margin: 4px;
            }
            QSplitter::handle:hover {
                background-color: #adb5bd;
            }
        """)

        # Modern inventory table container
        table_container = QFrame()
        table_container.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin: 0px;
            }
        """)
        table_layout = QVBoxLayout(table_container)
        table_layout.setSpacing(0)
        table_layout.setContentsMargins(0, 0, 0, 0)

        # Table header
        table_header = QLabel(tr("pos.inventory_table_title", "قائمة المخزون"))
        table_header.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #495057;
                padding: 20px 24px 16px 24px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #e9ecef;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                margin: 0px;
            }
        """)
        table_layout.addWidget(table_header)

        # Modern table design
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(6)
        self.inventory_table.setMinimumHeight(550)
        self.inventory_table.setHorizontalHeaderLabels([
            tr("pos.product_column", "المنتج"),
            tr("pos.description_column", "الوصف"),
            tr("pos.barcode_column", "الباركود"),
            tr("pos.stock_column", "المخزون"),
            tr("pos.min_stock_column", "الحد الأدنى"),
            tr("pos.actions_column", "الإجراءات")
        ])

        # Modern header style
        self.inventory_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #0d6efd;
                color: #ffffff;
                padding: 14px 12px;
                border: none;
                border-bottom: 1px solid #0b5ed7;
                font-weight: 600;
                font-size: 14px;
                text-align: center;
                min-height: 40px;
            }
            QHeaderView::section:hover {
                background-color: #0b5ed7;
            }
            QHeaderView::section:pressed {
                background-color: #0a58ca;
            }
        """)

        # Modern table style
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e7f3ff;
                border: none;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
                font-size: 14px;
                outline: none;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #e9ecef;
                color: #495057;
                text-align: center;
                min-height: 36px;
            }
            QTableWidget::item:selected {
                background-color: #e7f3ff;
                color: #0d6efd;
                font-weight: 500;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
                color: #495057;
            }
            QTableWidget::item:alternate {
                background-color: #f8f9fa;
            }
        """)

        # Modern column configuration
        self.inventory_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Product name
        self.inventory_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)  # Description
        self.inventory_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)  # Barcode
        self.inventory_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Stock
        self.inventory_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Min stock
        self.inventory_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)  # Actions

        # Optimized column widths
        self.inventory_table.setColumnWidth(1, 200)  # Description
        self.inventory_table.setColumnWidth(2, 140)  # Barcode
        self.inventory_table.setColumnWidth(5, 220)  # Actions

        # Table configuration
        self.inventory_table.verticalHeader().setVisible(False)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.SingleSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.selectionModel().selectionChanged.connect(self.on_inventory_selected)

        # Modern row height
        self.inventory_table.verticalHeader().setDefaultSectionSize(48)

        # Add table to container
        table_layout.addWidget(self.inventory_table)

        # Add table container to splitter
        content_splitter.addWidget(table_container)

        # Details frame with enhanced styling
        details_frame = QFrame()
        details_frame.setMinimumWidth(550)
        details_frame.setMaximumWidth(800)
        details_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9ff);
                border: 3px solid #2196f3;
                border-radius: 12px;
                margin: 8px;
                box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
            }
        """)
        details_layout = QVBoxLayout(details_frame)
        details_layout.setSpacing(20)
        details_layout.setContentsMargins(25, 25, 25, 25)

        # Details tabs with enhanced height
        self.details_tabs = QTabWidget()
        self.details_tabs.setMinimumHeight(500)
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #2196f3;
                border-radius: 12px;
                background-color: #ffffff;
                margin-top: 8px;
                padding: 5px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9ff, stop:1 #e3f2fd);
                color: #1565c0;
                padding: 15px 30px;
                margin-right: 3px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
                border: 2px solid #e3f2fd;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: #ffffff;
                border-color: #2196f3;
                margin-bottom: -2px;
                padding-bottom: 17px;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #bbdefb, stop:1 #e3f2fd);
                color: #0d47a1;
                border-color: #2196f3;
                box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
            }
        """)

        # Details tab with enhanced layout
        details_tab = QWidget()
        details_tab_layout = QVBoxLayout(details_tab)
        details_tab_layout.setSpacing(25)
        details_tab_layout.setContentsMargins(25, 25, 25, 25)

        # Product details
        product_group = QGroupBox(tr("inventory.product_details_section", "تفاصيل المنتج"))
        product_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                color: #0d47a1;
                border: 3px solid #2196f3;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9ff, stop:1 #ffffff);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: #ffffff;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        product_layout = QFormLayout(product_group)

        # Set premium spacing and margins for the form layout
        product_layout.setSpacing(20)
        product_layout.setContentsMargins(25, 30, 25, 25)
        product_layout.setLabelAlignment(Qt.AlignLeft)
        product_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        product_layout.setRowWrapPolicy(QFormLayout.DontWrapRows)

        # Create enhanced styled labels for product information
        label_style = """
            QLabel {
                padding: 12px 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9ff);
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                color: #1565c0;
                min-height: 35px;
                selection-background-color: #e3f2fd;
            }
        """

        self.product_name_label = QLabel()
        self.product_name_label.setMinimumHeight(40)
        self.product_name_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.product_name_field", "اسم المنتج:"), self.product_name_label)

        self.product_category_label = QLabel()
        self.product_category_label.setMinimumHeight(40)
        self.product_category_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.description_field", "الوصف:"), self.product_category_label)

        self.product_barcode_label = QLabel()
        self.product_barcode_label.setMinimumHeight(40)
        self.product_barcode_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.barcode", "الباركود:"), self.product_barcode_label)

        details_tab_layout.addWidget(product_group)

        # Inventory details
        inventory_group = QGroupBox(tr("inventory.stock_details", "تفاصيل المخزون"))
        inventory_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                color: #0d47a1;
                border: 3px solid #2196f3;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9ff, stop:1 #ffffff);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: #ffffff;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_group)

        # Set premium spacing and margins for the inventory group
        inventory_layout.setSpacing(25)
        inventory_layout.setContentsMargins(25, 30, 25, 25)

        # Form layout for inventory details with enhanced spacing
        form_layout = QFormLayout()
        form_layout.setSpacing(22)
        form_layout.setLabelAlignment(Qt.AlignLeft)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        form_layout.setRowWrapPolicy(QFormLayout.DontWrapRows)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # Define enhanced input styling
        input_style = """
            QSpinBox, QLineEdit, QTextEdit {
                padding: 12px 16px;
                border: 2px solid #2196f3;
                border-radius: 8px;
                background-color: #ffffff;
                font-size: 14px;
                font-weight: 500;
                color: #1565c0;
                selection-background-color: #e3f2fd;
                min-height: 35px;
            }
            QSpinBox:focus, QLineEdit:focus, QTextEdit:focus {
                border-color: #0d47a1;
                outline: none;
                background-color: #f8f9ff;
                box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
            }
            QSpinBox:hover, QLineEdit:hover, QTextEdit:hover {
                border-color: #1976d2;
                background-color: #f3f8ff;
                box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                border: none;
                border-radius: 4px;
                width: 20px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42a5f5, stop:1 #2196f3);
            }
        """

        # Stock quantity with +/- buttons
        stock_layout = QHBoxLayout()
        stock_layout.setSpacing(15)
        stock_layout.setContentsMargins(5, 5, 5, 5)

        self.stock_quantity_spin = QSpinBox()
        self.stock_quantity_spin.setRange(0, 100000)
        self.stock_quantity_spin.setButtonSymbols(QSpinBox.NoButtons)
        self.stock_quantity_spin.setMinimumWidth(140)
        self.stock_quantity_spin.setMinimumHeight(45)
        self.stock_quantity_spin.setStyleSheet(input_style)
        stock_layout.addWidget(self.stock_quantity_spin)

        # Decrease button with enhanced styling
        decrease_button = QPushButton("-")
        decrease_button.setFixedSize(50, 50)
        decrease_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: #ffffff;
                border: 2px solid #d32f2f;
                border-radius: 10px;
                font-weight: bold;
                font-size: 20px;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef5350, stop:1 #f44336);
                border-color: #f44336;
                box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
                transform: scale(1.08);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
                border-color: #c62828;
                transform: scale(0.92);
                box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
            }
        """)
        decrease_button.clicked.connect(lambda: self.stock_quantity_spin.setValue(self.stock_quantity_spin.value() - 1))
        stock_layout.addWidget(decrease_button)

        # Increase button with premium styling
        increase_button = QPushButton("+")
        increase_button.setFixedSize(50, 50)
        increase_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                color: #ffffff;
                border: 2px solid #388e3c;
                border-radius: 10px;
                font-weight: bold;
                font-size: 20px;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66bb6a, stop:1 #4caf50);
                border-color: #4caf50;
                box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
                transform: scale(1.08);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #388e3c, stop:1 #2e7d32);
                border-color: #2e7d32;
                transform: scale(0.92);
                box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
            }
        """)
        increase_button.clicked.connect(lambda: self.stock_quantity_spin.setValue(self.stock_quantity_spin.value() + 1))
        stock_layout.addWidget(increase_button)

        form_layout.addRow(tr("inventory.current_quantity_field", "الكمية الحالية:"), stock_layout)

        # Min stock level
        self.min_stock_level_spin = QSpinBox()
        self.min_stock_level_spin.setRange(0, 100000)
        self.min_stock_level_spin.setMinimumHeight(45)
        self.min_stock_level_spin.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.min_stock_level_field", "الحد الأدنى للمخزون:"), self.min_stock_level_spin)

        # Location
        self.location_edit = QLineEdit()
        self.location_edit.setMinimumHeight(45)
        self.location_edit.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.location_field", "الموقع:"), self.location_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(120)
        self.notes_edit.setMinimumHeight(120)
        self.notes_edit.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), self.notes_edit)

        inventory_layout.addLayout(form_layout)

        details_tab_layout.addWidget(inventory_group)

        # Add spacing before buttons
        details_tab_layout.addSpacing(25)

        # Button container with better styling
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9ff, stop:1 #e3f2fd);
                border: 2px solid #bbdefb;
                border-radius: 12px;
                padding: 20px;
                margin: 15px 5px;
            }
        """)
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(20)
        button_layout.setContentsMargins(15, 15, 15, 15)

        # Save button
        self.save_button = QPushButton(tr("inventory.save_changes", "حفظ التغييرات"))
        self.save_button.setMinimumHeight(50)
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: #ffffff;
                border: 3px solid #1976d2;
                border-radius: 10px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 15px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42a5f5, stop:1 #2196f3);
                border-color: #2196f3;
                transform: translateY(-3px);
                box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #1565c0);
                border-color: #1565c0;
                transform: translateY(0px);
                box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3);
            }
        """)
        self.save_button.clicked.connect(self.save_inventory)
        button_layout.addWidget(self.save_button)

        # Add/remove stock buttons with enhanced spacing
        stock_buttons_layout = QHBoxLayout()
        stock_buttons_layout.setSpacing(20)
        stock_buttons_layout.setContentsMargins(5, 5, 5, 5)

        self.add_stock_button = QPushButton(tr("inventory.add_stock_button", "إضافة مخزون"))
        self.add_stock_button.setMinimumHeight(50)
        self.add_stock_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                color: #ffffff;
                border: 3px solid #388e3c;
                border-radius: 10px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 15px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66bb6a, stop:1 #4caf50);
                border-color: #4caf50;
                transform: translateY(-3px);
                box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #388e3c, stop:1 #2e7d32);
                border-color: #2e7d32;
                transform: translateY(0px);
                box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
            }
        """)
        self.add_stock_button.clicked.connect(self.add_stock)
        stock_buttons_layout.addWidget(self.add_stock_button)

        self.remove_stock_button = QPushButton(tr("inventory.remove_stock_button", "سحب مخزون"))
        self.remove_stock_button.setMinimumHeight(50)
        self.remove_stock_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: #ffffff;
                border: 3px solid #d32f2f;
                border-radius: 10px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 15px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef5350, stop:1 #f44336);
                border-color: #f44336;
                transform: translateY(-3px);
                box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
                border-color: #c62828;
                transform: translateY(0px);
                box-shadow: 0 3px 8px rgba(244, 67, 54, 0.3);
            }
        """)
        self.remove_stock_button.clicked.connect(self.remove_stock)
        stock_buttons_layout.addWidget(self.remove_stock_button)

        button_layout.addLayout(stock_buttons_layout)
        details_tab_layout.addWidget(button_container)

        # Spacer
        details_tab_layout.addStretch()

        self.details_tabs.addTab(details_tab, tr("pos.details", "التفاصيل"))

        # Transactions tab with enhanced layout
        transactions_tab = QWidget()
        transactions_layout = QVBoxLayout(transactions_tab)
        transactions_layout.setSpacing(15)
        transactions_layout.setContentsMargins(25, 25, 25, 25)

        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(5)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("inventory.date_column", "التاريخ"),
            tr("inventory.type_column", "النوع"),
            tr("inventory.quantity_column", "الكمية"),
            tr("inventory.reference_column", "المرجع"),
            tr("inventory.notes_column", "ملاحظات")
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Set enhanced row height for transactions table
        self.transactions_table.verticalHeader().setDefaultSectionSize(45)

        # Apply enhanced styling to transactions table
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e3f2fd;
                background-color: #ffffff;
                alternate-background-color: #f8fbff;
                selection-background-color: #e3f2fd;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                outline: none;
            }
            QTableWidget::item {
                padding: 12px 10px;
                border-bottom: 1px solid #e8f4fd;
                color: #2c3e50;
                text-align: center;
                min-height: 35px;
            }
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #0d47a1;
                font-weight: 600;
                border: 2px solid #2196f3;
                border-radius: 4px;
            }
            QTableWidget::item:hover {
                background-color: #f0f8ff;
                color: #1565c0;
                border-radius: 4px;
            }
        """)

        # Apply enhanced header styling to transactions table
        self.transactions_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: #ffffff;
                padding: 12px 10px;
                border: none;
                border-bottom: 3px solid #0d47a1;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
                min-height: 40px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #1565c0);
                box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            }
        """)

        transactions_layout.addWidget(self.transactions_table)

        self.details_tabs.addTab(transactions_tab, tr("pos.movements", "الحركات"))

        details_layout.addWidget(self.details_tabs)

        # Create a simple details panel (simplified for now)
        details_panel = QFrame()
        details_panel.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin: 0px;
            }
        """)
        details_layout = QVBoxLayout(details_panel)
        details_layout.setContentsMargins(24, 20, 24, 20)

        details_title = QLabel(tr("pos.product_details_title", "تفاصيل المنتج"))
        details_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #495057;
                padding: 16px 0px;
                border-bottom: 1px solid #e9ecef;
                margin-bottom: 16px;
            }
        """)
        details_layout.addWidget(details_title)
        details_layout.addStretch()

        # Add panels to splitter
        content_splitter.addWidget(details_panel)

        # Set proportions: 70% for table, 30% for details
        content_splitter.setSizes([700, 300])
        content_splitter.setStretchFactor(0, 70)
        content_splitter.setStretchFactor(1, 30)

        # Add splitter to main layout
        main_layout.addWidget(content_splitter)

        # Add enhanced status bar at the bottom
        self.status_label = QLabel(tr("pos.inventory_load_error_message", "Cannot update inventory view, please check database settings"))
        self.status_label.setMinimumHeight(60)
        self.status_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff8e1, stop:1 #fff3e0);
                color: #e65100;
                padding: 15px 25px;
                border: 3px solid #ffb74d;
                border-radius: 10px;
                font-size: 14px;
                font-weight: 600;
                text-align: center;
                letter-spacing: 0.5px;
                box-shadow: 0 2px 8px rgba(255, 183, 77, 0.2);
            }
        """)
        main_layout.addWidget(self.status_label)

        # Initialize details with default values instead of disabling
        self.init_details_with_defaults()

        # Set enhanced minimum size for the widget
        self.setMinimumSize(1400, 900)

    def init_details_with_defaults(self):
        """Initialize details with default values."""
        self.product_name_label.setText(tr("inventory.no_product_selected", "لم يتم اختيار منتج"))
        self.product_category_label.setText("-")
        self.product_barcode_label.setText("-")
        self.stock_quantity_spin.setValue(0)
        self.min_stock_level_spin.setValue(0)
        self.location_edit.setText("")
        self.notes_edit.setText(tr("pos.select_product_message", "Please click on a product to view details and manage inventory"))

        # Clear transactions table
        self.transactions_table.setRowCount(0)
        row = self.transactions_table.rowCount()
        self.transactions_table.insertRow(row)
        no_data_item = QTableWidgetItem(tr("inventory.no_transactions", "لا توجد حركات"))
        no_data_item.setTextAlignment(Qt.AlignCenter)
        self.transactions_table.setItem(row, 0, no_data_item)
        self.transactions_table.setSpan(row, 0, 1, 5)

    def load_data(self):
        """Load data from the database."""
        # Load categories
        self.load_categories()

        # Load inventory
        self.load_inventory()

        # Force translation update after data is loaded
        if self.translation_manager.current_language != 'ar':
            self.refresh_ui_translations()
            # Force immediate UI update
            self.update()
            self.repaint()

    def load_categories(self):
        """Load product categories."""
        try:
            # Get categories
            query = """
            SELECT * FROM categories
            ORDER BY name
            """
            rows = self.db_manager.execute_query(query)

            # Clear category combo
            self.category_combo.clear()

            # Add "All" option
            self.category_combo.addItem(tr("inventory.all_categories", "جميع الفئات"), None)

            # Add categories
            for category in rows:
                self.category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            # Just add "All" option if categories table doesn't exist
            self.category_combo.clear()
            self.category_combo.addItem(tr("inventory.all_categories", "جميع الفئات"), None)

    def load_inventory(self):
        """Load inventory items."""
        try:
            print("Loading POS inventory...")

            # Get inventory
            self.inventory_list = self.pos_manager.get_all_pos_inventory(
                include_zero_stock=self.show_zero_stock.isChecked(),
                category_id=self.category_combo.currentData()
            )

            print(f"Loaded {len(self.inventory_list)} inventory items")

            # If no inventory items found, try to sync from main inventory
            if not self.inventory_list:
                print("No POS inventory found, attempting to sync from main inventory...")
                try:
                    synced_count = self.pos_manager.sync_pos_inventory_from_main()
                    print(f"Synced {synced_count} products from main inventory")

                    # Reload inventory after sync
                    self.inventory_list = self.pos_manager.get_all_pos_inventory(
                        include_zero_stock=self.show_zero_stock.isChecked(),
                        category_id=self.category_combo.currentData()
                    )
                    print(f"After sync: {len(self.inventory_list)} inventory items")

                    # Update status message
                    if synced_count > 0:
                        self.status_label.setText(tr("inventory.sync_success", f"تم مزامنة {synced_count} منتج من المخزون الرئيسي"))
                        self.status_label.setStyleSheet("""
                            QLabel {
                                background-color: #e8f5e8;
                                color: #2e7d32;
                                padding: 12px 20px;
                                border: 2px solid #4caf50;
                                border-radius: 6px;
                                font-size: 14px;
                                font-weight: 500;
                            }
                        """)
                    else:
                        self.status_label.setText(tr("inventory.no_products_to_sync", "لا توجد منتجات للمزامنة من المخزون الرئيسي"))
                except Exception as sync_error:
                    print(f"Error syncing from main inventory: {sync_error}")
                    self.status_label.setText(tr("inventory.sync_error", f"خطأ في المزامنة: {str(sync_error)}"))

            # Display inventory
            self.display_inventory()

            # Update status if we have inventory - hide status label for clean interface
            if self.inventory_list:
                # Hide the status label completely for a cleaner interface
                self.status_label.hide()

        except Exception as e:
            print(f"Error loading inventory: {e}")
            self.inventory_list = []
            self.display_inventory()

            # Update status with error message
            self.status_label.setText(tr("inventory.load_error", f"خطأ في تحميل المخزون: {str(e)}"))
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #ffebee;
                    color: #c62828;
                    padding: 12px 20px;
                    border: 2px solid #f44336;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                }
            """)

    def display_inventory(self, filtered_inventory=None):
        """Display inventory items in the table.

        Args:
            filtered_inventory (list, optional): Pre-filtered inventory list. If None, will call filter_inventory().
        """
        # Clear table
        self.inventory_table.setRowCount(0)

        # Filter inventory if not provided
        if filtered_inventory is None:
            filtered_inventory = self.filter_inventory()

        # Add inventory items to table
        for inventory in filtered_inventory:
            row = self.inventory_table.rowCount()
            self.inventory_table.insertRow(row)

            # Product name
            name_item = QTableWidgetItem(inventory.product_name or "")
            name_item.setData(Qt.UserRole, inventory.id)
            self.inventory_table.setItem(row, 0, name_item)

            # Category
            category_item = QTableWidgetItem(inventory.category_name or "")
            self.inventory_table.setItem(row, 1, category_item)

            # Barcode
            barcode_item = QTableWidgetItem(inventory.product_barcode or "")
            self.inventory_table.setItem(row, 2, barcode_item)

            # Stock quantity
            stock_item = QTableWidgetItem(str(inventory.stock_quantity))
            # Highlight low stock
            if inventory.min_stock_level > 0 and inventory.stock_quantity <= inventory.min_stock_level:
                stock_item.setBackground(Qt.red)
                stock_item.setForeground(Qt.white)
            self.inventory_table.setItem(row, 3, stock_item)

            # Min stock level
            min_stock_item = QTableWidgetItem(str(inventory.min_stock_level))
            self.inventory_table.setItem(row, 4, min_stock_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            actions_layout.setSpacing(8)
            actions_layout.setAlignment(Qt.AlignCenter)

            # Store inventory ID for later use
            inventory_id = inventory.id

            # Edit button
            edit_button = QPushButton(tr("inventory.edit_button", "تعديل"))
            edit_button.setProperty("inventory_id", inventory_id)
            edit_button.setCursor(Qt.PointingHandCursor)
            edit_button.setIcon(QIcon("resources/icons/edit.png"))
            edit_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2196f3, stop:1 #1976d2);
                    color: #ffffff;
                    border: 2px solid #1976d2;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11px;
                    text-align: center;
                    min-width: 50px;
                    min-height: 28px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #42a5f5, stop:1 #2196f3);
                    border-color: #2196f3;
                    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
                    transform: translateY(-1px);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1976d2, stop:1 #0d47a1);
                    border-color: #0d47a1;
                    transform: translateY(0px);
                }
            """)
            # Use lambda to capture the current inventory ID
            edit_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.edit_inventory(inv_id))
            actions_layout.addWidget(edit_button)

            # Add stock button
            add_stock_button = QPushButton("+")
            add_stock_button.setProperty("inventory_id", inventory_id)
            add_stock_button.setCursor(Qt.PointingHandCursor)
            add_stock_button.setToolTip(tr("inventory.add_stock", "إضافة مخزون"))
            add_stock_button.setMaximumWidth(30)
            add_stock_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4caf50, stop:1 #388e3c);
                    color: #ffffff;
                    border: 2px solid #388e3c;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 14px;
                    text-align: center;
                    min-width: 28px;
                    min-height: 28px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #66bb6a, stop:1 #4caf50);
                    border-color: #4caf50;
                    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #388e3c, stop:1 #2e7d32);
                    border-color: #2e7d32;
                    transform: scale(0.95);
                }
            """)
            # Use lambda to capture the current inventory ID
            add_stock_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.quick_add_stock(inv_id))
            actions_layout.addWidget(add_stock_button)

            # Remove stock button
            remove_stock_button = QPushButton("-")
            remove_stock_button.setProperty("inventory_id", inventory_id)
            remove_stock_button.setCursor(Qt.PointingHandCursor)
            remove_stock_button.setToolTip(tr("inventory.remove_stock", "سحب مخزون"))
            remove_stock_button.setMaximumWidth(30)
            remove_stock_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f44336, stop:1 #d32f2f);
                    color: #ffffff;
                    border: 2px solid #d32f2f;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 14px;
                    text-align: center;
                    min-width: 28px;
                    min-height: 28px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ef5350, stop:1 #f44336);
                    border-color: #f44336;
                    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d32f2f, stop:1 #b71c1c);
                    border-color: #b71c1c;
                    transform: scale(0.95);
                }
            """)
            # Use lambda to capture the current inventory ID
            remove_stock_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.quick_remove_stock(inv_id))
            actions_layout.addWidget(remove_stock_button)

            self.inventory_table.setCellWidget(row, 5, actions_widget)

    def filter_inventory(self):
        """Filter inventory based on search text and category.

        Returns:
            list: Filtered inventory
        """
        # Get search text
        search_text = self.search_edit.text().strip().lower()

        # Get selected category
        category_id = self.category_combo.currentData()

        # Filter inventory
        filtered_inventory = []
        for inventory in self.inventory_list:
            # Check if inventory matches search text
            if search_text and search_text not in (inventory.product_name or "").lower() and search_text not in (inventory.product_barcode or "").lower():
                continue

            # Check if inventory matches category
            if category_id and inventory.category_id != category_id:
                continue

            # Check if we should show zero stock
            if not self.show_zero_stock.isChecked() and inventory.stock_quantity <= 0:
                continue

            filtered_inventory.append(inventory)

        return filtered_inventory

    def refresh_ui_translations(self):
        """Refresh all UI translations when language changes."""
        print("=== POS INVENTORY VIEW: refresh_ui_translations CALLED ===")
        print(f"Current language: {self.translation_manager.current_language}")

        # Use the new translation update method
        self.apply_language_settings()















    def on_inventory_selected(self):
        """Handle inventory selection."""
        selected_items = self.inventory_table.selectedItems()
        if not selected_items:
            self.enable_details(False)
            self.selected_inventory_id = None
            return

        # Get inventory ID
        inventory_id = selected_items[0].data(Qt.UserRole)
        self.selected_inventory_id = inventory_id

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
        if not inventory_item:
            self.enable_details(False)
            return

        # Update details
        self.product_name_label.setText(inventory_item.product_name or "")
        self.product_category_label.setText(inventory_item.category_name or "")
        self.product_barcode_label.setText(inventory_item.product_barcode or "")

        self.stock_quantity_spin.setValue(inventory_item.stock_quantity)
        self.min_stock_level_spin.setValue(inventory_item.min_stock_level)
        self.location_edit.setText(inventory_item.location or "")
        self.notes_edit.setText(inventory_item.notes or "")

        # Load transactions
        self.load_transactions(inventory_id)

        # Enable details
        self.enable_details(True)

    def load_transactions(self, inventory_id):
        """Load transactions for an inventory item.

        Args:
            inventory_id (int): Inventory ID
        """
        # Clear table
        self.transactions_table.setRowCount(0)

        # Get transactions
        transactions = self.pos_manager.get_pos_inventory_transactions(inventory_id=inventory_id)

        # Add transactions to table
        for transaction in transactions:
            row = self.transactions_table.rowCount()
            self.transactions_table.insertRow(row)

            # Date
            date_item = QTableWidgetItem(format_date(transaction.created_at))
            self.transactions_table.setItem(row, 0, date_item)

            # Type
            type_text = ""
            if transaction.transaction_type == POSInventoryTransaction.TYPE_PURCHASE:
                type_text = tr("inventory.purchase_type", "شراء")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_SALE:
                type_text = tr("inventory.sale_type", "بيع")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_ADJUSTMENT:
                type_text = tr("inventory.adjustment_type", "تعديل")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_TRANSFER_IN:
                type_text = tr("inventory.transfer_in_type", "تحويل وارد")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_TRANSFER_OUT:
                type_text = tr("inventory.transfer_out_type", "تحويل صادر")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_RETURN:
                type_text = tr("inventory.return_type", "مرتجع")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_INITIAL:
                type_text = tr("inventory.initial_type", "رصيد افتتاحي")

            type_item = QTableWidgetItem(type_text)
            self.transactions_table.setItem(row, 1, type_item)

            # Quantity
            quantity_item = QTableWidgetItem(str(transaction.quantity))
            self.transactions_table.setItem(row, 2, quantity_item)

            # Reference
            reference_text = ""
            if transaction.reference_type == 'invoice':
                reference_text = tr("inventory.invoice_ref", "فاتورة #{0}").format(transaction.reference_id)
            elif transaction.reference_type == 'manual':
                reference_text = tr("inventory.manual_ref", "يدوي")

            reference_item = QTableWidgetItem(reference_text)
            self.transactions_table.setItem(row, 3, reference_item)

            # Notes
            notes_item = QTableWidgetItem(transaction.notes or "")
            self.transactions_table.setItem(row, 4, notes_item)

    def init_details_with_defaults(self):
        """Initialize details panel with default values."""
        # Set default values for product details
        self.product_name_label.setText(tr("inventory.no_product_selected", "لم يتم اختيار منتج"))
        self.product_category_label.setText("-")
        self.product_barcode_label.setText("-")

        # Set default values for inventory details
        self.stock_quantity_spin.setValue(0)
        self.min_stock_level_spin.setValue(0)
        self.location_edit.setText("")
        self.notes_edit.setText(tr("pos.select_product_message", "Please click on a product to view details and manage inventory"))

        # Enable the details panel but disable editing
        self.details_tabs.setEnabled(True)
        self.save_button.setEnabled(False)
        self.add_stock_button.setEnabled(False)
        self.remove_stock_button.setEnabled(False)

        # Clear transactions table
        self.transactions_table.setRowCount(0)

        # Add a hint row to transactions table
        self.transactions_table.insertRow(0)
        hint_item = QTableWidgetItem(tr("inventory.no_transactions", "لا توجد حركات"))
        hint_item.setTextAlignment(Qt.AlignCenter)
        self.transactions_table.setSpan(0, 0, 1, 5)
        self.transactions_table.setItem(0, 0, hint_item)

    def enable_details(self, enabled):
        """Enable or disable details widgets.

        Args:
            enabled (bool): Whether to enable the widgets
        """
        # If not enabled, show default values
        if not enabled:
            self.init_details_with_defaults()
            return

        # Otherwise enable editing controls
        self.details_tabs.setEnabled(True)
        self.save_button.setEnabled(True)
        self.add_stock_button.setEnabled(True)
        self.remove_stock_button.setEnabled(True)



    def save_inventory(self):
        """Save inventory changes."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Update inventory item
        inventory_item.stock_quantity = self.stock_quantity_spin.value()
        inventory_item.min_stock_level = self.min_stock_level_spin.value()
        inventory_item.location = self.location_edit.text()
        inventory_item.notes = self.notes_edit.toPlainText()

        # Save changes
        if self.pos_manager.update_pos_inventory_item(inventory_item):
            QMessageBox.information(
                self,
                tr("messages.success", "نجاح"),
                tr("inventory.adjustment_success", "تم التعديل بنجاح")
            )

            # Reload inventory
            self.load_data()

            # Emit signal
            self.inventory_updated.emit()
        else:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.update_error", "حدث خطأ أثناء تحديث المخزون")
            )

    def add_stock(self):
        """Add stock to inventory."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Show dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.add_stock", "إضافة مخزون"))
        dialog.setMinimumWidth(300)

        # Layout
        layout = QVBoxLayout(dialog)

        # Form
        form_layout = QFormLayout()

        # Product name
        name_label = QLabel(inventory_item.product_name)
        name_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), name_label)

        # Current stock
        current_stock_label = QLabel(str(inventory_item.stock_quantity))
        form_layout.addRow(tr("inventory.current_stock", "المخزون الحالي:"), current_stock_label)

        # Quantity to add
        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, 100000)
        quantity_spin.setValue(1)
        form_layout.addRow(tr("inventory.quantity_to_add", "الكمية المضافة:"), quantity_spin)

        # Notes
        notes_edit = QLineEdit()
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Add transaction
            try:
                # Ensure we have a valid notes value
                notes_text = notes_edit.text()
                if not notes_text or notes_text.strip() == "":
                    notes_text = tr("inventory.manual_addition", "إضافة يدوية")

                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_item.id,
                    transaction_type=POSInventoryTransaction.TYPE_PURCHASE,
                    quantity=quantity_spin.value(),
                    reference_type='manual',
                    notes=notes_text
                )
            except Exception as e:
                print(f"Error creating transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.stock_added", "تمت إضافة {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity_spin.value(), inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Reload transactions
                self.load_transactions(self.selected_inventory_id)

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون")
                )

    def remove_stock(self):
        """Remove stock from inventory."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Check if there's enough stock
        if inventory_item.stock_quantity <= 0:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.no_stock", "لا يوجد مخزون كافي")
            )
            return

        # Show dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.remove_stock", "سحب مخزون"))
        dialog.setMinimumWidth(300)

        # Layout
        layout = QVBoxLayout(dialog)

        # Form
        form_layout = QFormLayout()

        # Product name
        name_label = QLabel(inventory_item.product_name)
        name_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), name_label)

        # Current stock
        current_stock_label = QLabel(str(inventory_item.stock_quantity))
        form_layout.addRow(tr("inventory.current_stock", "المخزون الحالي:"), current_stock_label)

        # Quantity to remove
        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, inventory_item.stock_quantity)
        quantity_spin.setValue(1)
        form_layout.addRow(tr("inventory.quantity_to_remove", "الكمية المسحوبة:"), quantity_spin)

        # Notes
        notes_edit = QLineEdit()
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Add transaction
            try:
                # Ensure we have a valid notes value
                notes_text = notes_edit.text()
                if not notes_text or notes_text.strip() == "":
                    notes_text = tr("inventory.manual_removal", "سحب يدوي")

                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_item.id,
                    transaction_type=POSInventoryTransaction.TYPE_ADJUSTMENT,
                    quantity=quantity_spin.value(),
                    reference_type='decrease',
                    notes=notes_text
                )
            except Exception as e:
                print(f"Error creating transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.stock_removed", "تم سحب {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity_spin.value(), inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Reload transactions
                self.load_transactions(self.selected_inventory_id)

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
                )

    def add_new_product(self):
        """Add a new product to POS inventory."""
        # Get products from main inventory that are not in POS inventory
        query = """
        SELECT p.*
        FROM products p
        LEFT JOIN pos_inventory pi ON p.id = pi.product_id
        WHERE pi.id IS NULL AND p.type = 'product'
        ORDER BY p.name
        """

        rows = self.db_manager.execute_query(query)
        if not rows:
            QMessageBox.information(
                self,
                tr("messages.info", "معلومات"),
                tr("inventory.no_products_to_add", "لا توجد منتجات جديدة للإضافة. جميع المنتجات موجودة بالفعل في مخزون نقاط البيع.")
            )
            return

        # Create dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.add_product", "إضافة منتج"))
        dialog.setMinimumWidth(400)

        # Layout
        layout = QVBoxLayout(dialog)

        # Product selection
        form_layout = QFormLayout()

        # Product combo
        product_combo = QComboBox()
        for row in rows:
            product_combo.addItem(row['name'], row['id'])
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), product_combo)

        # Initial stock
        stock_spin = QSpinBox()
        stock_spin.setRange(0, 10000)
        stock_spin.setValue(0)
        form_layout.addRow(tr("inventory.initial_stock", "المخزون الأولي:"), stock_spin)

        # Min stock level
        min_stock_spin = QSpinBox()
        min_stock_spin.setRange(0, 10000)
        min_stock_spin.setValue(0)
        form_layout.addRow(tr("inventory.min_stock_level", "الحد الأدنى للمخزون:"), min_stock_spin)

        # Location
        location_edit = QLineEdit()
        form_layout.addRow(tr("inventory.location_field", "الموقع:"), location_edit)

        # Notes
        notes_edit = QTextEdit()
        notes_edit.setMaximumHeight(100)
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Get selected product
            product_id = product_combo.currentData()

            # Create inventory item
            inventory_item = POSInventory(
                product_id=product_id,
                stock_quantity=stock_spin.value(),
                min_stock_level=min_stock_spin.value(),
                location=location_edit.text(),
                notes=notes_edit.toPlainText()
            )

            # Add to POS inventory
            inventory_id = self.pos_manager.add_pos_inventory_item(inventory_item)

            if inventory_id > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.product_added", "تمت إضافة المنتج بنجاح")
                )

                # Reload inventory
                self.load_data()

                # Select the new item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.product_add_error", "حدث خطأ أثناء إضافة المنتج")
                )

    def edit_inventory(self, inventory_id=None):
        """Edit inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to edit. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Select the item in the table to show details
            for row in range(self.inventory_table.rowCount()):
                item = self.inventory_table.item(row, 0)
                if item and item.data(Qt.UserRole) == inventory_id:
                    self.inventory_table.selectRow(row)
                    break

            # Switch to details tab
            self.details_tabs.setCurrentIndex(0)
        except Exception as e:
            print(f"Error editing inventory: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.edit_error", "حدث خطأ أثناء تعديل المنتج: {0}").format(str(e))
            )

    def quick_add_stock(self, inventory_id=None):
        """Quickly add stock to inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to add stock to. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Ask for quantity
            quantity, ok = QInputDialog.getInt(
                self,
                tr("inventory.add_stock_button", "إضافة مخزون"),
                tr("inventory.enter_quantity", "أدخل الكمية:"),
                1, 1, 1000, 1
            )

            if not ok:
                return

            # Add transaction
            try:
                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_id,
                    transaction_type=POSInventoryTransaction.TYPE_PURCHASE,
                    quantity=quantity,
                    reference_type='manual',
                    notes=tr("inventory.quick_add", "إضافة سريعة")
                )
            except Exception as e:
                print(f"Error creating quick add transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                # Show confirmation
                QMessageBox.information(
                    self,
                    tr("inventory.success", "نجاح"),
                    tr("inventory.stock_added", "تمت إضافة {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity, inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Select the item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون")
                )
        except Exception as e:
            print(f"Error adding stock: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون: {0}").format(str(e))
            )

    def quick_remove_stock(self, inventory_id=None):
        """Quickly remove stock from inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to remove stock from. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Check if there's enough stock
            if inventory_item.stock_quantity <= 0:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.no_stock", "لا يوجد مخزون كافي")
                )
                return

            # Ask for quantity
            quantity, ok = QInputDialog.getInt(
                self,
                tr("inventory.remove_stock_button", "سحب مخزون"),
                tr("inventory.enter_quantity", "أدخل الكمية:"),
                1, 1, inventory_item.stock_quantity, 1
            )

            if not ok:
                return

            # Add transaction
            try:
                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_id,
                    transaction_type=POSInventoryTransaction.TYPE_ADJUSTMENT,
                    quantity=quantity,
                    reference_type='decrease',
                    notes=tr("inventory.quick_remove", "سحب سريع")
                )
            except Exception as e:
                print(f"Error creating quick remove transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                # Show confirmation
                QMessageBox.information(
                    self,
                    tr("inventory.success", "نجاح"),
                    tr("inventory.stock_removed", "تم سحب {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity, inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Select the item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
                )
        except Exception as e:
            print(f"Error removing stock: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون: {0}").format(str(e))
            )

            # Select the item
            for row in range(self.inventory_table.rowCount()):
                item = self.inventory_table.item(row, 0)
                if item and item.data(Qt.UserRole) == inventory_id:
                    self.inventory_table.selectRow(row)
                    break
        else:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
            )

    def sync_from_main(self):
        """Sync inventory from main inventory."""
        # Confirm
        result = QMessageBox.question(
            self,
            tr("inventory.confirm", "تأكيد"),
            tr("inventory.sync_confirm", "هل تريد مزامنة المنتجات من المخزون الرئيسي؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if result != QMessageBox.Yes:
            return

        # Sync
        count = self.pos_manager.sync_pos_inventory_from_main()

        if count > 0:
            QMessageBox.information(
                self,
                tr("inventory.success", "نجاح"),
                tr("inventory.sync_success", "تمت مزامنة {0} منتج من المخزون الرئيسي").format(count)
            )

            # Reload inventory
            self.load_data()

            # Emit signal
            self.inventory_updated.emit()
        else:
            QMessageBox.information(
                self,
                tr("inventory.info", "معلومات"),
                tr("inventory.no_new_products", "لا توجد منتجات جديدة للمزامنة")
            )

    def showEvent(self, event):
        """Handle show event."""
        super().showEvent(event)
        # Force update highlighted elements when view is shown
        if hasattr(self, 'translation_manager') and self.translation_manager.current_language != 'ar':
            self.force_update_highlighted_elements()
