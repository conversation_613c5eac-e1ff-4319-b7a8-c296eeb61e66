#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
User Management View for فوترها (Fawterha)
Provides a view for managing users (cashiers, managers, admins)
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QLineEdit, QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
    QCheckBox, QGroupBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from database.pos_manager import POSManager
from models.user import User
from utils.translation_manager import tr


class UserDialog(QDialog):
    """Dialog for adding and editing users."""

    def __init__(self, db_manager, user=None, parent=None):
        """Initialize the user dialog.

        Args:
            db_manager: Database manager instance
            user (User, optional): User to edit. Defaults to None.
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.pos_manager = POSManager(db_manager)
        self.user = user
        self.user_id = None
        self.is_editing = user is not None

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        if self.is_editing:
            self.setWindowTitle(tr("users.edit_user", "تعديل المستخدم"))
        else:
            self.setWindowTitle(tr("users.new_user", "مستخدم جديد"))

        self.setMinimumWidth(400)
        self.setMinimumHeight(350)
        self.setWindowIcon(QIcon("resources/icons/user.png"))
        self.setLayoutDirection(Qt.RightToLeft)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Form layout
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(10)

        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(tr("users.username_placeholder", "اسم المستخدم"))
        self.username_edit.setMinimumHeight(40)
        form_layout.addRow(tr("users.username", "اسم المستخدم:"), self.username_edit)

        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText(tr("users.password_placeholder", "كلمة المرور"))
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(40)
        form_layout.addRow(tr("users.password", "كلمة المرور:"), self.password_edit)

        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("users.name_placeholder", "الاسم الكامل"))
        self.name_edit.setMinimumHeight(40)
        form_layout.addRow(tr("users.name", "الاسم:"), self.name_edit)

        # Role
        self.role_combo = QComboBox()
        self.role_combo.addItem(tr("users.role_admin", "مدير النظام"), User.ROLE_ADMIN)
        self.role_combo.addItem(tr("users.role_manager", "مدير"), User.ROLE_MANAGER)
        self.role_combo.addItem(tr("users.role_cashier", "كاشير"), User.ROLE_CASHIER)
        self.role_combo.addItem(tr("users.role_accountant", "محاسب"), User.ROLE_ACCOUNTANT)
        self.role_combo.addItem(tr("users.role_inventory", "مشرف مخزون"), User.ROLE_INVENTORY)
        self.role_combo.setMinimumHeight(40)
        form_layout.addRow(tr("users.role", "الدور:"), self.role_combo)

        # Role description
        self.role_description = QLabel()
        self.role_description.setStyleSheet("color: #555; font-style: italic;")
        self.role_description.setWordWrap(True)
        form_layout.addRow("", self.role_description)

        # Connect role combo to update description
        self.role_combo.currentIndexChanged.connect(self.update_role_description)

        # Active
        self.active_check = QCheckBox(tr("users.is_active", "نشط"))
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)

        main_layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_user)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def update_role_description(self):
        """Update the role description based on the selected role."""
        role = self.role_combo.currentData()

        if role == User.ROLE_ADMIN:
            self.role_description.setText(tr("users.role_admin_desc", "جميع الصلاحيات في النظام"))
        elif role == User.ROLE_MANAGER:
            self.role_description.setText(tr("users.role_manager_desc", "معظم الصلاحيات باستثناء إدارة المستخدمين"))
        elif role == User.ROLE_CASHIER:
            self.role_description.setText(tr("users.role_cashier_desc", "صلاحيات محدودة لعمليات البيع فقط"))
        elif role == User.ROLE_ACCOUNTANT:
            self.role_description.setText(tr("users.role_accountant_desc", "صلاحيات متعلقة بالحسابات والتقارير المالية"))
        elif role == User.ROLE_INVENTORY:
            self.role_description.setText(tr("users.role_inventory_desc", "صلاحيات متعلقة بإدارة المخزون والمنتجات"))
        else:
            self.role_description.setText("")

    def load_data(self):
        """Load user data if editing."""
        if self.is_editing and self.user:
            self.username_edit.setText(self.user.username)
            self.name_edit.setText(self.user.name)

            # Set role
            index = self.role_combo.findData(self.user.role)
            if index >= 0:
                self.role_combo.setCurrentIndex(index)

            # Set active
            self.active_check.setChecked(self.user.is_active)

            # Disable username field when editing
            self.username_edit.setEnabled(False)

            # Clear password field (don't show existing password)
            self.password_edit.clear()
            self.password_edit.setPlaceholderText(tr("users.password_edit_placeholder", "اترك فارغاً للاحتفاظ بكلمة المرور الحالية"))

        # Update role description
        self.update_role_description()

    def save_user(self):
        """Save the user."""
        # Get data
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        name = self.name_edit.text().strip()
        role = self.role_combo.currentData()
        is_active = self.active_check.isChecked()

        # Validate data
        if not username:
            QMessageBox.warning(
                self,
                tr("errors.required_field", "حقل مطلوب"),
                tr("users.username_required", "يرجى إدخال اسم المستخدم")
            )
            self.username_edit.setFocus()
            return

        if not self.is_editing and not password:
            QMessageBox.warning(
                self,
                tr("errors.required_field", "حقل مطلوب"),
                tr("users.password_required", "يرجى إدخال كلمة المرور")
            )
            self.password_edit.setFocus()
            return

        if not name:
            QMessageBox.warning(
                self,
                tr("errors.required_field", "حقل مطلوب"),
                tr("users.name_required", "يرجى إدخال الاسم")
            )
            self.name_edit.setFocus()
            return

        # Create or update user
        if self.is_editing:
            # Update user
            self.user.name = name
            self.user.role = role
            self.user.is_active = is_active

            # Update user in database
            if self.pos_manager.update_user(self.user):
                # Update password if provided
                if password:
                    self.pos_manager.update_user_password(self.user.id, password)

                self.user_id = self.user.id
                QMessageBox.information(
                    self,
                    tr("users.user_updated", "تم تحديث المستخدم"),
                    tr("users.user_updated_message", "تم تحديث بيانات المستخدم بنجاح")
                )
                self.accept()
            else:
                QMessageBox.critical(
                    self,
                    tr("errors.database_error", "خطأ في قاعدة البيانات"),
                    tr("users.update_error", "حدث خطأ أثناء تحديث المستخدم")
                )
        else:
            # Check if username already exists
            existing_user = self.pos_manager.get_user_by_username(username)
            if existing_user:
                QMessageBox.warning(
                    self,
                    tr("users.username_exists", "اسم المستخدم موجود"),
                    tr("users.username_exists_message", "اسم المستخدم موجود بالفعل. يرجى اختيار اسم مستخدم آخر")
                )
                self.username_edit.setFocus()
                return

            # Create new user
            user = User(
                username=username,
                password=password,
                name=name,
                role=role,
                is_active=is_active
            )

            # Add user to database
            self.user_id = self.pos_manager.add_user(user)
            if self.user_id > 0:
                QMessageBox.information(
                    self,
                    tr("users.user_created", "تم إضافة المستخدم"),
                    tr("users.user_created_message", "تم إضافة المستخدم بنجاح")
                )
                self.accept()
            else:
                QMessageBox.critical(
                    self,
                    tr("errors.database_error", "خطأ في قاعدة البيانات"),
                    tr("users.add_error", "حدث خطأ أثناء إضافة المستخدم")
                )

    def get_user_id(self):
        """Get the ID of the created or updated user.

        Returns:
            int: User ID or None if canceled
        """
        return self.user_id


class UserManagementView(QWidget):
    """Widget for managing users."""

    def __init__(self, db_manager, parent=None):
        """Initialize the user management view.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.pos_manager = POSManager(db_manager)
        self.users = []

        self.setup_ui()
        self.load_users()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Title
        title_label = QLabel(tr("users.management", "إدارة المستخدمين"))
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #0d47a1;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Description
        description_label = QLabel(tr("users.management_description", "إدارة حسابات المستخدمين (الكاشير، المديرين، مدراء النظام)"))
        description_label.setStyleSheet("font-size: 12pt; color: #555;")
        description_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(description_label)

        # Toolbar
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        toolbar_layout.setSpacing(10)

        # Add user button
        self.add_button = QPushButton(tr("users.add_user", "إضافة مستخدم"))
        self.add_button.setIcon(QIcon("resources/icons/add.png"))
        self.add_button.clicked.connect(self.add_user)
        toolbar_layout.addWidget(self.add_button)

        # Refresh button
        self.refresh_button = QPushButton(tr("common.refresh", "تحديث"))
        self.refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        self.refresh_button.clicked.connect(self.load_users)
        toolbar_layout.addWidget(self.refresh_button)

        toolbar_layout.addStretch()

        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("users.search", "بحث..."))
        self.search_edit.setMinimumWidth(200)
        self.search_edit.textChanged.connect(self.filter_users)
        toolbar_layout.addWidget(self.search_edit)

        main_layout.addLayout(toolbar_layout)

        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            tr("users.username", "اسم المستخدم"),
            tr("users.name", "الاسم"),
            tr("users.role", "الدور"),
            tr("users.status", "الحالة"),
            tr("users.created_at", "تاريخ الإنشاء"),
            tr("users.actions", "الإجراءات")
        ])
        # Set header styling - theme-aware
        self.users_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                padding: 12px 8px;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
                border: 1px solid palette(mid);
            }
        """)

        self.users_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.setAlternatingRowColors(True)

        # Set row height for better appearance - increased height
        self.users_table.verticalHeader().setDefaultSectionSize(65)
        self.users_table.verticalHeader().setMinimumSectionSize(55)

        # Set table styling - theme-aware
        self.users_table.setStyleSheet("""
            QTableWidget {
                gridline-color: palette(mid);
                background-color: palette(base);
                alternate-background-color: palette(alternate-base);
                selection-background-color: palette(highlight);
                border: 1px solid palette(mid);
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid palette(mid);
                color: palette(text);
            }
            QTableWidget::item:selected {
                background-color: palette(highlight);
                color: palette(highlighted-text);
            }
            QTableWidget::item:hover {
                background-color: palette(midlight);
            }
        """)

        main_layout.addWidget(self.users_table)

    def load_users(self):
        """Load users from the database."""
        # Get users
        self.users = self.pos_manager.get_all_users()

        # Display users
        self.display_users()

    def display_users(self):
        """Display users in the table."""
        # Clear table
        self.users_table.setRowCount(0)

        # Add users to table
        for user in self.users:
            row = self.users_table.rowCount()
            self.users_table.insertRow(row)

            # Username
            username_item = QTableWidgetItem(user.username)
            username_item.setData(Qt.UserRole, user.id)
            username_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            self.users_table.setItem(row, 0, username_item)

            # Name
            name_item = QTableWidgetItem(user.name)
            name_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            self.users_table.setItem(row, 1, name_item)

            # Role
            role_text = ""
            if user.role == User.ROLE_ADMIN:
                role_text = tr("users.role_admin", "مدير النظام")
            elif user.role == User.ROLE_MANAGER:
                role_text = tr("users.role_manager", "مدير")
            elif user.role == User.ROLE_CASHIER:
                role_text = tr("users.role_cashier", "كاشير")
            elif user.role == User.ROLE_ACCOUNTANT:
                role_text = tr("users.role_accountant", "محاسب")
            elif user.role == User.ROLE_INVENTORY:
                role_text = tr("users.role_inventory", "مشرف مخزون")

            role_item = QTableWidgetItem(role_text)
            role_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            self.users_table.setItem(row, 2, role_item)

            # Status
            status_text = tr("users.active", "نشط") if user.is_active else tr("users.inactive", "غير نشط")
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            status_item.setForeground(Qt.darkGreen if user.is_active else Qt.darkRed)
            self.users_table.setItem(row, 3, status_item)

            # Created at
            created_at_item = QTableWidgetItem(str(user.created_at))
            created_at_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            self.users_table.setItem(row, 4, created_at_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(8, 12, 8, 12)
            actions_layout.setSpacing(10)

            # Edit button
            edit_button = QPushButton(tr("common.edit", "تعديل"))
            edit_button.setProperty("user_id", user.id)
            edit_button.clicked.connect(self.edit_user)
            edit_button.setMinimumHeight(36)
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 8px 14px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 60px;
                }
                QPushButton:hover {
                    background-color: #0b5ed7;
                }
                QPushButton:pressed {
                    background-color: #0a58ca;
                }
            """)
            actions_layout.addWidget(edit_button)

            # Reset password button
            reset_button = QPushButton(tr("users.reset_password", "إعادة تعيين كلمة المرور"))
            reset_button.setProperty("user_id", user.id)
            reset_button.clicked.connect(self.reset_password)
            reset_button.setMinimumHeight(36)
            reset_button.setStyleSheet("""
                QPushButton {
                    background-color: #fd7e14;
                    color: white;
                    border: none;
                    padding: 8px 14px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #e8690b;
                }
                QPushButton:pressed {
                    background-color: #d56309;
                }
            """)
            actions_layout.addWidget(reset_button)

            # Delete button
            delete_button = QPushButton(tr("common.delete", "حذف"))
            delete_button.setProperty("user_id", user.id)
            delete_button.clicked.connect(self.delete_user)
            delete_button.setMinimumHeight(36)
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 8px 14px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 50px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)
            actions_layout.addWidget(delete_button)

            self.users_table.setCellWidget(row, 5, actions_widget)

    def filter_users(self):
        """Filter users based on search text."""
        search_text = self.search_edit.text().strip().lower()

        # If search text is empty, show all users
        if not search_text:
            for row in range(self.users_table.rowCount()):
                self.users_table.setRowHidden(row, False)
            return

        # Hide rows that don't match search text
        for row in range(self.users_table.rowCount()):
            username = self.users_table.item(row, 0).text().lower()
            name = self.users_table.item(row, 1).text().lower()
            role = self.users_table.item(row, 2).text().lower()

            if search_text in username or search_text in name or search_text in role:
                self.users_table.setRowHidden(row, False)
            else:
                self.users_table.setRowHidden(row, True)

    def add_user(self):
        """Add a new user."""
        dialog = UserDialog(self.db_manager, parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.load_users()

    def edit_user(self):
        """Edit a user."""
        button = self.sender()
        if not button:
            return

        user_id = button.property("user_id")
        if not user_id:
            return

        # Find user
        user = None
        for u in self.users:
            if u.id == user_id:
                user = u
                break

        if not user:
            return

        # Show dialog
        dialog = UserDialog(self.db_manager, user, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_users()

    def reset_password(self):
        """Reset a user's password."""
        button = self.sender()
        if not button:
            return

        user_id = button.property("user_id")
        if not user_id:
            return

        # Find user
        user = None
        for u in self.users:
            if u.id == user_id:
                user = u
                break

        if not user:
            return

        # Show password reset dialog
        from PySide6.QtWidgets import QInputDialog

        new_password, ok = QInputDialog.getText(
            self,
            tr("users.reset_password", "إعادة تعيين كلمة المرور"),
            tr("users.new_password", "كلمة المرور الجديدة:"),
            QLineEdit.Password
        )

        if not ok or not new_password:
            return

        # Confirm password
        confirm_password, ok = QInputDialog.getText(
            self,
            tr("users.reset_password", "إعادة تعيين كلمة المرور"),
            tr("users.confirm_password", "تأكيد كلمة المرور:"),
            QLineEdit.Password
        )

        if not ok or not confirm_password:
            return

        # Check if passwords match
        if new_password != confirm_password:
            QMessageBox.warning(
                self,
                tr("users.password_mismatch", "كلمات المرور غير متطابقة"),
                tr("users.password_mismatch_message", "كلمات المرور غير متطابقة. يرجى المحاولة مرة أخرى.")
            )
            return

        # Update password
        if self.pos_manager.update_user_password(user_id, new_password):
            QMessageBox.information(
                self,
                tr("users.password_reset", "تم إعادة تعيين كلمة المرور"),
                tr("users.password_reset_message", "تم إعادة تعيين كلمة المرور بنجاح")
            )

            # Log activity
            from database.activity_log_manager import get_activity_log_manager
            activity_log_manager = get_activity_log_manager(self.db_manager)
            if activity_log_manager:
                activity_log_manager.log_activity(
                    user_id=None,  # Current user ID should be used here
                    username="admin",  # Current username should be used here
                    activity_type="update",
                    entity_type="user",
                    entity_id=user_id,
                    description=f"تم إعادة تعيين كلمة المرور للمستخدم {user.username}"
                )
        else:
            QMessageBox.critical(
                self,
                tr("errors.database_error", "خطأ في قاعدة البيانات"),
                tr("users.password_reset_error", "حدث خطأ أثناء إعادة تعيين كلمة المرور")
            )

    def delete_user(self):
        """Delete a user."""
        button = self.sender()
        if not button:
            return

        user_id = button.property("user_id")
        if not user_id:
            return

        # Find user
        user = None
        for u in self.users:
            if u.id == user_id:
                user = u
                break

        if not user:
            return

        # Confirm deletion
        response = QMessageBox.question(
            self,
            tr("users.confirm_delete", "تأكيد الحذف"),
            tr("users.confirm_delete_message", "هل أنت متأكد من حذف المستخدم {0}؟").format(user.name),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if response != QMessageBox.Yes:
            return

        # Delete user
        if self.pos_manager.delete_user(user_id):
            QMessageBox.information(
                self,
                tr("users.user_deleted", "تم حذف المستخدم"),
                tr("users.user_deleted_message", "تم حذف المستخدم بنجاح")
            )

            # Log activity
            from database.activity_log_manager import get_activity_log_manager
            activity_log_manager = get_activity_log_manager(self.db_manager)
            if activity_log_manager:
                activity_log_manager.log_activity(
                    user_id=None,  # Current user ID should be used here
                    username="admin",  # Current username should be used here
                    activity_type="delete",
                    entity_type="user",
                    entity_id=user_id,
                    description=f"تم حذف المستخدم {user.username}"
                )

            self.load_users()
        else:
            QMessageBox.critical(
                self,
                tr("errors.database_error", "خطأ في قاعدة البيانات"),
                tr("users.delete_error", "حدث خطأ أثناء حذف المستخدم")
            )
