{"app_name": "<PERSON><PERSON><PERSON><PERSON>", "language_name": "English", "direction": "ltr", "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "print": "Print", "export": "Export", "import": "Import", "refresh": "Refresh", "close": "Close", "back": "Back", "next": "Next", "yes": "Yes", "no": "No", "ok": "OK", "confirm": "Confirm", "select": "Select", "all": "All", "none": "None", "date": "Date", "time": "Time", "amount": "Amount", "total": "Total", "subtotal": "Subtotal", "discount": "Discount", "tax": "Tax", "price": "Price", "quantity": "Quantity", "description": "Description", "name": "Name", "id": "ID", "code": "Code", "status": "Status", "note": "Note", "notes": "Notes", "from": "From", "to": "To", "details": "Details", "view": "View", "month": "Month", "year": "Year", "search_by": "Search by", "add_new": "Add New", "tax_rate": "Tax Rate", "default": "<PERSON><PERSON><PERSON>", "creation_date": "Creation Date", "inventory_management": "Inventory Management", "inventory_movement": "Inventory Movement", "add_inventory": "Add Inventory", "adjust_stock": "Adjust Stock", "min_stock": "Minimum Stock", "current_stock": "Current Stock", "state": "State", "statistics": "Statistics", "invoice_count": "Invoice Count", "total_invoices": "Total Invoices", "total_paid": "Total Paid", "total_remaining": "Total Remaining", "fully_paid_invoices": "Fully Paid Invoices", "invoices_with_balance": "Invoices with Balance"}, "menu": {"dashboard": "Dashboard", "invoices": "Invoices", "customers": "Customers", "products": "Products", "services": "Services", "inventory": "Inventory", "reports": "Reports", "settings": "Settings", "help": "Help", "about": "About", "logout": "Logout"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Fawterha", "total_sales": "Total Sales", "total_invoices": "Total Invoices", "paid_invoices": "Paid Invoices", "unpaid_invoices": "Unpaid Invoices", "partially_paid": "Partially Paid", "recent_invoices": "Recent Invoices", "recent_customers": "Recent Customers", "sales_overview": "Sales Overview", "monthly_sales": "Monthly Sales", "yearly_sales": "Yearly Sales"}, "invoices": {"title": "Invoices", "new_invoice": "New Invoice", "edit_invoice": "Edit Invoice", "view_invoice": "View Invoice", "invoice_number": "Invoice Number", "invoice_date": "Invoice Date", "due_date": "Due Date", "issue_date": "Issue Date", "customer": "Customer", "items": "Items", "add_item": "Add Item", "remove_item": "Remove Item", "paid_amount": "<PERSON><PERSON>", "remaining_amount": "Remaining Amount", "payment_status": "Payment Status", "paid": "Paid", "unpaid": "Unpaid", "partially_paid": "Partially Paid", "canceled": "Canceled", "invoice_total": "Invoice Total", "create_invoice": "Create Invoice", "update_invoice": "Update Invoice", "delete_invoice": "Delete Invoice", "print_invoice": "Print Invoice", "export_invoice": "Export Invoice", "original_value": "Original Value: {0}", "all_invoices": "All Invoices", "draft": "Draft", "pending": "Pending", "cancelled": "Cancelled", "search_invoice": "Search invoice...", "invoice": "Invoice", "invoice_created": "Invoice created successfully", "invoice_updated": "Invoice updated successfully", "invoice_deleted": "Invoice deleted successfully", "add_item_manually": "Add Product/Service Manually", "select_from_products": "Select from Products and Services", "invoice_prefix": "Invoice Number Prefix", "next_invoice_number": "Next Invoice Number", "tax_rate": "Tax Rate", "invoice_not_found": "Invoice not found", "confirm_delete_message": "Are you sure you want to delete invoice {0}?\n\nThis action cannot be undone.", "delete_error": "Error deleting invoice: {0}", "inventory_linked": "This invoice contains products linked to inventory. Do you want to restore inventory and delete the invoice?", "cannot_delete": "Cannot delete invoice because it contains products linked to inventory"}, "customers": {"title": "Customers", "new_customer": "New Customer", "edit_customer": "Edit Customer", "view_customer": "View Customer", "customer_name": "Customer Name", "customer_email": "Email", "customer_phone": "Phone", "customer_address": "Address", "customer_since": "Customer Since", "total_purchases": "Total Purchases", "create_customer": "Create Customer", "update_customer": "Update Customer", "delete_customer": "Delete Customer", "customer_created": "Customer created successfully", "customer_updated": "Customer updated successfully", "customer_deleted": "Customer deleted successfully", "customer_statement": "Customer Statement", "search_customer": "Search Customer", "view_account_statement": "View Account Statement", "add_new_customer": "Add New Customer", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "statement": "Statement"}, "products": {"title": "Products", "new_product": "New Product", "edit_product": "Edit Product", "view_product": "View Product", "product_name": "Product Name", "product_code": "Product Code", "product_price": "Product Price", "product_description": "Product Description", "in_stock": "In Stock", "stock_quantity": "Stock Quantity", "min_stock": "Minimum Stock", "track_inventory": "Track Inventory", "create_product": "Create Product", "update_product": "Update Product", "delete_product": "Delete Product", "product_created": "Product created successfully", "product_updated": "Product updated successfully", "product_deleted": "Product deleted successfully", "products": "Products", "services": "Services", "add_new_product": "Add New Product", "add_new_service": "Add New Service", "add_new": "Add New Product/Service", "edit": "Edit Product/Service", "name": "Name:", "enter_name": "Enter product or service name", "enter_description": "Enter product or service description", "price": "Price:", "tax_rate": "Tax Rate:", "type": "Type:", "product_type": "Product", "service_type": "Service", "search_service": "Search service...", "load_products_error": "Error loading products: {0}", "load_services_error": "Error loading services: {0}", "search_product": "Search product", "description": "Description", "tax_rate_short": "Tax"}, "services": {"title": "Services", "new_service": "New Service", "edit_service": "Edit Service", "view_service": "View Service", "service_name": "Service Name", "service_code": "Service Code", "service_price": "Service Price", "service_description": "Service Description", "create_service": "Create Service", "update_service": "Update Service", "delete_service": "Delete Service", "service_created": "Service created successfully", "service_updated": "Service updated successfully", "service_deleted": "Service deleted successfully"}, "inventory": {"title": "Inventory", "low_stock": "Low Stock", "out_of_stock": "Out of Stock", "restock": "Restock", "adjust_stock": "Adjust Stock", "stock_history": "Stock History", "current_stock": "Current Stock", "products": "Products", "services": "Services", "add_new_product": "Add New Product", "search_product": "Search Product", "inventory_management": "Inventory Management", "inventory_movement": "Inventory Movement", "add_inventory": "Add Inventory", "add_stock": "Add Stock", "products_management": "Products Inventory Management", "stock_movement": "Stock Movement", "product": "Product", "available_quantity": "Available Quantity", "min_level": "Minimum Level", "status": "Status", "transaction_type": "Transaction Type", "quantity": "Quantity", "reference": "Reference", "purchase": "Purchase", "sale": "Sale", "adjustment": "Adjustment", "return": "Return", "not_tracked": "Not Tracked", "available": "Available", "track_inventory": "Track Inventory", "current_quantity": "Current Quantity", "min_stock_level": "Minimum Stock Level", "management": "Inventory Management", "warehouses": "Warehouses", "warehouses_description": "Manage warehouses and their locations", "add_warehouse": "Add Warehouse", "edit_warehouse": "Edit Warehouse", "warehouse_inventory": "Warehouse Inventory", "feature_coming_soon": "This feature is coming soon", "confirm_delete_warehouse": "Are you sure you want to delete warehouse '{0}'?", "warehouse_deleted": "Warehouse deleted successfully", "warehouse_in_use": "Cannot delete warehouse because it contains products", "warehouse_not_found": "Warehouse not found", "warehouse_updated": "Warehouse updated successfully", "warehouse_added": "Warehouse added successfully", "update_error": "Error updating warehouse", "add_error": "Error adding warehouse", "save_error": "Error saving warehouse", "name_required": "Warehouse name is required", "location": "Location", "description": "Description", "name": "Name", "view_inventory": "View Inventory", "default_warehouse": "Main Warehouse", "show_inactive": "Show inactive warehouses", "alerts": "Inventory Alerts", "alerts_description": "View and manage low stock alerts and products nearing expiry", "check_alerts": "<PERSON>s", "mark_all_read": "<PERSON> as <PERSON>", "filter_by_type": "Filter by Type", "all_types": "All Types", "expiry": "Expiry", "show_read": "Show Read Alerts", "alert_type": "Alert <PERSON>", "warehouse": "Warehouse", "message": "Message", "date": "Date", "auto_refresh": "Auto-refresh every 5 minutes", "mark_read": "<PERSON> <PERSON>", "view_product": "View Product", "confirm_delete_alert": "Are you sure you want to delete this alert?", "new_alerts": "New Alerts", "new_alerts_created": "{0} new alerts created", "all_marked_read": "All alerts marked as read", "product_details": "Product Details", "alerts_count": "Alerts count: {0}", "expiry_date": "Expiry Date", "no_expiry": "No expiry date", "barcode": "Barcode", "barcode_placeholder": "Will be generated automatically"}, "reports": {"title": "Reports", "sales_report": "Sales Report", "customer_report": "Customer Report", "inventory_report": "Inventory Report", "tax_report": "Tax Report", "profit_loss": "Profit & Loss", "generate_report": "Generate Report", "report_period": "Report Period", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "custom_period": "Custom Period", "export_report": "Export Report", "total_sales": "Total Sales", "total_paid": "Total Paid", "total_remaining": "Total Remaining", "total_canceled": "Total Canceled", "total_invoices_with_balance": "Total Invoices with Balance", "monthly_report": "Monthly Report", "filter_report": "Filter Report", "export_pdf": "Export to PDF", "export_excel": "Export to Excel", "refresh_report": "Refresh Report", "month": "Month", "invoice_count": "Invoice Count", "report_type": "Report Type", "monthly_statistics": "Monthly Statistics", "reload_report": "Reload Report", "fully_paid_invoices": "Fully Paid Invoices", "cancelled_invoices": "Cancelled Invoices", "from": "From", "to": "To", "report_results": "Report Results", "totals": "Statistics"}, "settings": {"title": "Settings", "general": "General", "appearance": "Appearance", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "taxes": "Taxes", "backup": "Backup", "restore": "Rest<PERSON>", "user_profile": "User Profile", "company_info": "Company Information", "invoice_settings": "Invoice Settings", "default_settings": "<PERSON><PERSON><PERSON>", "theme": "Theme", "default_theme": "Default Theme", "dark_theme": "Dark Theme", "dreamy_theme": "Dreamy Theme", "space_theme": "Space Theme", "galaxy_theme": "Galaxy Theme", "mono_theme": "Mono Theme", "vintage_theme": "Vintage Theme", "save_settings": "Save Settings", "settings_saved": "Setting<PERSON> saved successfully", "invoice_templates": "Invoice Templates", "add_new_template": "Add New Template", "template_name": "Template Name", "is_default": "<PERSON>", "company_logo": "Logo", "select_logo": "Select Logo", "remove_logo": "Remove Logo", "select_logo_title": "Select Logo", "image_files": "Images (*.png *.jpg *.jpeg)", "image_not_recognized": "Image file not recognized", "no_logo": "No Logo", "theme_settings": "Theme Settings", "theme_preview_title": "Theme Preview", "sample_button": "<PERSON><PERSON>", "sample_input": "Sample Input Field", "preview": "Preview", "language_settings": "Language Settings", "language_description": "Change the user interface language of the application", "language_note": "Note: Language changes will be applied after restarting the application", "reset": "Reset"}, "currency": {"title": "Currencies", "primary_currency": "Primary Currency", "exchange_rates": "Exchange Rates", "update_rates": "Update Rates", "add_currency": "Add <PERSON>cy", "edit_currency": "<PERSON>", "delete_currency": "Delete Currency", "currency_name": "Currency Name", "currency_code": "Currency Code", "currency_symbol": "Currency Symbol", "exchange_rate": "Exchange Rate", "egyptian_pound": "Egyptian Pound", "sar_symbol": "SAR", "currency_management": "Currency Management", "add_new_currency": "Add New Currency", "active": "Active", "is_primary": "Is Primary", "set_as_primary": "Set as Primary", "short_code": "Short Code", "name": "Name", "symbol": "Symbol", "is_active": "Active", "management": "Currency Management"}, "users": {"management": "User Management", "management_description": "Manage user accounts (cashiers, managers, administrators)", "add_user": "Add User", "edit_user": "Edit User", "new_user": "New User", "username": "Username", "password": "Password", "name": "Name", "role": "Role", "status": "Status", "created_at": "Created At", "actions": "Actions", "username_placeholder": "Username", "password_placeholder": "Password", "password_edit_placeholder": "Leave empty to keep current password", "name_placeholder": "Full Name", "role_admin": "Administrator", "role_manager": "Manager", "role_cashier": "Cashier", "role_accountant": "Accountant", "role_inventory": "Inventory Supervisor", "role_admin_desc": "All permissions in the system", "role_manager_desc": "Most permissions except user management", "role_cashier_desc": "Limited permissions for sales operations only", "role_accountant_desc": "Permissions related to accounting and financial reports", "role_inventory_desc": "Permissions related to inventory and product management", "is_active": "Active", "active": "Active", "inactive": "Inactive", "search": "Search...", "username_required": "Please enter a username", "password_required": "Please enter a password", "name_required": "Please enter a name", "username_exists": "Username exists", "username_exists_message": "Username already exists. Please choose a different username", "user_created": "User Added", "user_created_message": "User has been added successfully", "user_updated": "User Updated", "user_updated_message": "User has been updated successfully", "user_deleted": "User Deleted", "user_deleted_message": "User has been deleted successfully", "confirm_delete": "Confirm Delete", "confirm_delete_message": "Are you sure you want to delete user {0}?", "add_error": "Error adding user", "update_error": "Error updating user", "delete_error": "Error deleting user", "reset_password": "Reset Password", "new_password": "New Password:", "confirm_password": "Confirm Password:", "password_mismatch": "Passwords don't match", "password_mismatch_message": "Passwords don't match. Please try again.", "password_reset": "Password Reset", "password_reset_message": "Password has been reset successfully", "password_reset_error": "Error resetting password"}, "activity_log": {"title": "Activity Log", "description": "Description", "filters": "Filters", "activity_type": "Activity Type:", "entity_type": "Entity Type:", "start_date": "Start Date:", "end_date": "End Date:", "search": "Search...", "all_types": "All Types", "all_entities": "All Entities", "type_login": "<PERSON><PERSON>", "type_logout": "Logout", "type_create": "Create", "type_update": "Update", "type_delete": "Delete", "type_view": "View", "type_export": "Export", "type_import": "Import", "type_print": "Print", "entity_user": "User", "entity_product": "Product", "entity_customer": "Customer", "entity_invoice": "Invoice", "entity_transaction": "Transaction", "entity_inventory": "Inventory", "entity_pos_session": "POS Session", "entity_account": "Account", "entity_system": "System", "id": "ID", "user": "User", "created_at": "Date", "actions": "Actions", "details": "Details", "ip_address": "IP Address", "entity_id": "Entity ID", "page": "Page {0}"}, "pos": {"system_name": "Point of Sale System", "login_title": "Login to POS System", "login_subtitle": "Please login to access the Point of Sale system", "login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "username_placeholder": "Enter username", "password_placeholder": "Enter password", "login_error": "<PERSON><PERSON>", "username_required": "Please enter a username", "password_required": "Please enter a password", "invalid_credentials": "Invalid username or password", "no_pos_access": "You don't have permission to access the POS system", "products_load_error": "Error loading products: {0}", "cash": "Cash", "card": "Credit Card", "bank_transfer": "Bank Transfer", "session": "Session", "open_session": "Open New POS Session", "close_session": "Close POS Session", "close_session_question": "Do you want to close the current POS session?", "session_details": "Session Details", "session_summary": "Session Summary", "cashier": "Cashier", "starting_cash": "Starting Cash", "ending_cash": "Ending Cash", "notes": "Notes", "notes_placeholder": "Additional notes...", "closing_notes_placeholder": "Closing notes...", "start_time": "Start Time", "total_sales": "Total Sales", "cash_payments": "Cash Payments", "card_payments": "Card Payments", "other_payments": "Other Payments", "expected_cash": "Expected Cash", "cash_difference": "Difference", "session_opened": "Session Opened", "session_opened_message": "POS session opened successfully", "session_closed": "Session Closed", "session_closed_message": "POS session closed successfully", "session_error": "Session Error", "already_open_session": "You already have an open session. Please close the current session first", "error_opening_session": "Error opening session", "error_closing_session": "Error closing session", "search_products": "Search products...", "scan_barcode": "Scan Barcode", "enter_barcode": "Enter barcode:", "product_not_found": "Product not found", "barcode_not_found": "No product found with this barcode", "all_categories": "All Categories", "customer": "Customer", "add_customer": "Add Customer", "walk_in_customer": "Walk-in Customer", "product": "Product", "price": "Price", "quantity": "Quantity", "discount": "Discount", "total": "Total", "actions": "Actions", "edit": "Edit", "remove": "Remove", "totals": "Totals", "subtotal": "Subtotal", "tax": "Tax", "percentage": "%", "amount": "Amount", "clear": "Clear", "hold": "Hold", "pay": "Pay", "empty_cart": "Empty Cart", "empty_cart_message": "Cannot complete a sale with an empty cart", "hold_invoice": "Hold Invoice", "hold_note": "Note (optional):", "invoice_held": "Invoice Held", "invoice_held_message": "Invoice has been held successfully", "payment": "Payment", "back": "Back", "payment_methods": "Payment Methods", "reference": "Reference", "reference_placeholder": "Card number, transfer number, etc...", "paid_amount": "<PERSON><PERSON>", "change": "Change", "invoice_summary": "Invoice Summary", "items_count": "Items Count", "options": "Options", "print_receipt": "Print Receipt", "email_receipt": "Email Receipt", "complete_sale": "Complete Sale", "missing_reference": "Missing Reference", "reference_required": "Please enter a reference for the selected payment method", "insufficient_payment": "Insufficient Payment", "insufficient_payment_message": "The paid amount is less than the invoice total", "transaction_error": "Transaction Error", "transaction_error_message": "Error creating transaction", "sale_completed": "Sale Completed", "sale_completed_message": "Sale completed successfully", "sale_completed_with_change": "Sale completed successfully. Change: {0}", "invoice_error": "Invoice Error", "invoice_error_message": "Error creating invoice", "item_error": "<PERSON><PERSON>", "item_error_message": "Error adding item: {0}", "printer_not_configured": "Printer Not Configured", "printer_not_configured_message": "Thermal printer is not configured. Please configure the printer in settings", "printing": "Printing", "printing_message": "Printing receipt...", "emailing": "Emailing", "emailing_message": "Emailing receipt...", "edit_item": "<PERSON>em", "sales_screen": "Sales Screen", "inventory_screen": "Inventory", "version": "Version", "system_version": "System Version", "add_product": "Add Product", "revenue": "Revenue", "expenses": "Expenses", "profit": "Profit", "payment_type": "Payment Type", "payment_amount": "Payment Amount", "apply_discount": "Apply Discount", "discount_type": "Discount Type", "fixed_amount": "Fixed Amount", "percentage_discount": "Percentage", "discount_value": "Discount Value", "apply": "Apply", "cancel": "Cancel", "barcode_scan": "Barcode Scan", "manual_entry": "Manual Entry", "search_by_name": "Search by Name", "search_by_code": "Search by Code", "search_results": "Search Results", "no_results": "No results found", "select": "Select", "close": "Close", "save": "Save", "delete": "Delete", "confirm": "Confirm", "confirm_delete": "Confirm Delete", "confirm_delete_message": "Are you sure you want to delete this item?", "item_deleted": "Item Deleted", "item_deleted_message": "Item has been deleted successfully", "delete_error": "Error deleting item", "delete_error_message": "Error deleting item: {0}", "product_list": "Product List", "add_to_cart": "Add to Cart", "remove_from_cart": "Remove from Cart", "checkout": "Checkout", "receipt": "Receipt", "receipt_number": "Receipt Number", "cashier_name": "Cashier", "sale_date": "Sale Date", "sale_time": "Sale Time", "payment_method": "Payment Method", "cash_drawer": "Cash Drawer", "open_drawer": "Open Drawer", "close_drawer": "Close Drawer", "drawer_balance": "Drawer <PERSON>lance", "drawer_count": "Drawer Count", "drawer_difference": "Drawer Difference", "drawer_opening_amount": "Opening Amount", "drawer_closing_amount": "Closing Amount", "drawer_cash_sales": "Cash Sales", "drawer_cash_refunds": "Cash Refunds", "drawer_cash_payouts": "Cash Payouts", "drawer_cash_drops": "Cash Drops", "drawer_expected_amount": "Expected Amount", "drawer_actual_amount": "Actual Amount", "drawer_over_short": "Over/Short", "drawer_notes": "Notes", "drawer_close_confirmation": "Are you sure you want to close the cash drawer?", "drawer_close_success": "Cash drawer closed successfully", "drawer_close_error": "Error closing cash drawer", "sales_register": "Sales Register", "pos_system": "Point of Sale System", "customer_selection": "Customer Selection", "select_customer": "Select Customer", "product_selection": "Product Selection", "select_product": "Select Product", "add_discount_button": "Add Discount", "apply_discount_button": "Apply Discount", "clear_cart_button": "Clear Cart", "hold_button": "Hold", "pay_button": "Pay", "checkout_button": "Checkout", "cart_items": "Cart Items", "cart_empty": "Cart is empty", "cart_total": "Cart Total", "item_price": "<PERSON><PERSON>", "item_quantity": "Quantity", "item_discount": "Discount", "item_total": "Total", "subtotal_label": "Subtotal", "tax_label": "Tax", "total_label": "Total", "payment_label": "Payment", "change_label": "Change", "version_label": "Version", "system_version_label": "System Version", "register_number": "Register #", "cashier_label": "Cashier", "date_label": "Date", "time_label": "Time", "inventory_management": "Inventory Management", "pos_inventory_management": "POS Inventory Management", "inventory_report": "Inventory Report", "show_products_with_low_stock": "Show products with low stock", "product_name": "Product Name", "current_stock": "Current Stock:", "min_stock": "<PERSON>", "stock_value": "Stock Value", "product_details": "Product Details", "stock_movement": "Stock Movement", "current_quantity": "Current Quantity", "minimum_quantity": "Minimum Quantity", "save_changes": "Save Changes", "add_stock": "Add Stock", "remove_stock": "Remove Stock", "stock_adjustment": "Stock Adjustment", "adjustment_type": "Adjustment", "adjustment_quantity": "Adjustment Quantity", "adjustment_reason": "Adjustment Reason", "adjustment_date": "Adjustment Date", "add_adjustment": "Add Adjustment", "cancel_adjustment": "Cancel Adjustment", "adjustment_success": "Successfully updated", "adjustment_error": "Adjustment Error", "inventory_value": "Inventory Value", "total_items": "Total Items", "low_stock_items": "Low Stock Items", "out_of_stock_items": "Out of Stock Items", "inventory_summary": "Inventory Summary", "inventory_alerts": "Inventory Alerts", "inventory_transactions": "Inventory Transactions", "transaction_date": "Transaction Date", "transaction_type": "Transaction Type:", "transaction_quantity": "Quantity", "transaction_reference": "Reference", "transaction_notes": "Notes", "filter_by_date": "Filter by Date", "filter_by_type": "Filter by Type", "all_transactions": "All Transactions", "purchase": "Purchase", "sale": "Sale", "adjustment": "Adjustment", "return": "Return", "initial": "Initial", "apply_filter": "Apply", "clear_filter": "Clear Filter", "no_transactions": "No transactions", "no_products": "No products found", "back_to_inventory": "Back to Inventory", "manage_pos_inventory": "Manage POS Inventory", "pos_inventory_management_title": "POS Inventory Management", "inventory_manager": "Inventory Manager", "back_to_main_inventory": "Back to Main Inventory", "product_column": "Product", "description_column": "Description", "stock_column": "Stock", "min_stock_column": "<PERSON>", "actions_column": "Actions", "edit_action": "Edit", "delete_action": "Delete", "products_tab": "Products", "movements_tab": "Movements", "product_info_section": "Product Information", "product_name_field": "Product Name:", "description_field": "Description:", "stock_field": "Stock", "stock_details_section": "Stock Details", "current_value_field": "Current Value", "min_stock_value_field": "Min Stock Value", "cost_field": "Cost", "notes_field": "Notes", "please_click_on_product": "Please click on a product to view details and manage inventory", "add_product_button": "Add Product", "session_info": "Session #{0}", "logout_button": "Logout", "system_manager": "System Manager", "current_quantity_field": "Current Quantity:", "min_stock_level_field": "Min Stock Level:", "location_field": "Location:", "product_details_section": "Product Details", "add_stock_button": "Add Stock", "remove_stock_button": "Remove Stock", "search_products_placeholder": "Search for products...", "stock_details": "Stock Details", "movements": "Movements", "details": "Details", "select_product_message": "Please click on a product to view details and manage inventory", "no_product_selected": "No product selected", "ok": "OK", "quick_add": "Quick Add", "quick_remove": "Quick Remove", "enter_quantity": "Enter quantity:", "sync_confirm": "Do you want to sync products from main inventory?", "sync_success": "Successfully synced {0} products from main inventory", "no_new_products": "No new products to sync", "success": "Success", "info": "Information", "inventory_not_found": "Product not found in inventory", "edit_error": "Error editing product: {0}", "transaction_create_error": "Error creating transaction: {0}", "stock_added": "Successfully added {0} units of product '{1}'", "stock_removed": "Successfully removed {0} units of product '{1}'", "barcode": "Barcode", "edit_button": "Edit", "date_column": "Date", "type_column": "Type", "quantity_column": "Quantity", "reference_column": "Reference", "notes_column": "Notes", "purchase_type": "Purchase", "sale_type": "Sale", "transfer_in_type": "Transfer In", "transfer_out_type": "Transfer Out", "return_type": "Return", "initial_type": "Initial", "invoice_ref": "Invoice #{0}", "manual_ref": "Manual", "update_error": "Error updating inventory", "quantity_to_add": "Quantity to Add:", "manual_addition": "Manual Addition", "no_stock": "No stock available", "quantity_to_remove": "Quantity to Remove:", "manual_removal": "Manual Removal", "stock_add_error": "Error adding stock", "stock_remove_error": "Error removing stock", "no_products_to_add": "No new products to add. All products are already in POS inventory.", "initial_stock": "Initial Stock:", "min_stock_level": "<PERSON>", "inventory_load_error": "Error loading inventory data: {0}", "show_zero_stock": "Show Zero Stock Items", "sync_from_main": "Sync from Main Inventory", "sync_from_main_button": "Sync from Main Inventory", "barcode_column": "Barcode", "product_added": "Product added successfully", "location": "Location", "current_inventory": "Current Inventory", "low_stock": "Low Stock", "export_to_excel": "Export to Excel", "refresh": "Refresh", "search": "Search:", "search_placeholder": "Search for product...", "product_id": "Product ID", "stock_quantity": "Quantity", "status": "Status", "total_products": "Total Products: {0}", "total_quantity": "Total Quantity: {0}", "low_stock_count": "Low Stock Items: {0}", "date_range": "Date Range:", "to": "to", "date": "Date", "user": "User", "out_of_stock": "Out of Stock", "in_stock": "In Stock", "needed": "Needed", "print_low_stock": "Print Low Stock Report", "export_low_stock": "Export Low Stock Report", "data_load_error": "Error loading data: {0}", "unknown": "Unknown", "inventory_load_error_message": "Cannot update inventory view, please check database settings", "no_products_to_sync": "No products to sync from main inventory", "sync_error": "Error syncing: {0}", "loaded_successfully": "Successfully loaded {0} items", "load_error": "Error loading inventory: {0}", "inventory_details": "Inventory Details"}, "errors": {"required_field": "This field is required", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number", "invalid_date": "Invalid date", "invalid_number": "Invalid number", "invalid_amount": "Invalid amount", "connection_error": "Connection error", "database_error": "Database error", "unknown_error": "Unknown error", "operation_failed": "Operation failed"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "confirm_cancel": "Are you sure you want to cancel?", "confirm_exit": "Are you sure you want to exit?", "changes_saved": "Changes saved successfully", "operation_successful": "Operation completed successfully", "no_data": "No data available", "loading": "Loading...", "warning": "Warning", "error": "Error", "success": "Success", "confirm_delete_title": "Confirm Delete"}, "templates": {"title": "Invoice Templates", "description": "Create and customize invoice templates to suit your business needs. You can change colors, fonts, and display settings for each template.", "add_new": "Add New Template", "edit": "Edit Template", "name": "Template Name", "name_label": "Template Name:", "name_placeholder": "Enter template name", "description_placeholder": "Enter template description", "use_as_default": "Use as default template", "colors": "Colors", "header_color": "Header Color:", "text_color": "Text Color:", "accent_color": "Accent Color:", "font": "Font", "font_family": "Font Family:", "font_size": "Font Size:", "show_logo": "Show Logo", "show_header": "Show Header", "show_footer": "Show Footer", "footer_text": "Footer Text:", "preview": "Template Preview", "save": "Save", "cancel": "Cancel", "is_default": "<PERSON><PERSON><PERSON>", "creation_date": "Creation Date", "basic_settings": "Basic Settings", "name_required": "Template name is required", "add_success": "Template added successfully", "add_error": "Error adding template", "update_success": "Template updated successfully", "update_error": "Error updating template", "delete_success": "Template deleted successfully", "delete_error": "Error deleting template", "not_found": "Temp<PERSON> not found", "cannot_delete_default": "Cannot delete default template", "confirm_delete": "Are you sure you want to delete template '{0}'?"}, "accounting": {"general_ledger": "General <PERSON><PERSON>", "accounts_payable_receivable": "Accounts Payable & Receivable", "accounts_receivable": "Accounts Receivable", "accounts_payable": "Accounts Payable", "financial_reports": "Financial Reports", "income_statement": "Income Statement", "balance_sheet": "Balance Sheet", "add_transaction": "Add Transaction", "add_payment": "Add Payment", "date_range": "Date Range", "to": "To", "from": "From", "account": "Account", "all_accounts": "All Accounts", "status": "Status", "all_statuses": "All Statuses", "posted": "Posted", "draft": "Draft", "voided": "Voided", "apply_filter": "Apply Filter", "transactions": "Transactions", "transaction_details": "Transaction Details", "date": "Date", "number": "Number", "description": "Description", "reference": "Reference", "amount": "Amount", "actions": "Actions", "debit": "Debit", "credit": "Credit", "totals": "Totals", "post": "Post", "void": "Void", "confirm_post": "Confirm Post", "confirm_post_message": "Are you sure you want to post this transaction?", "post_success": "Posted", "post_success_message": "Transaction posted successfully", "post_error": "Post Error", "post_error_message": "Error posting transaction", "confirm_void": "Confirm Void", "confirm_void_message": "Are you sure you want to void this transaction? This will reverse its effect on account balances.", "void_success": "Voided", "void_success_message": "Transaction voided successfully", "void_error": "Void Error", "void_error_message": "Error voiding transaction", "transaction_number": "Transaction Number", "transaction_date": "Transaction Date", "add_detail": "Add Detail", "total_debit": "Total Debit", "total_credit": "Total Credit", "difference": "Difference", "view_transaction": "View Transaction", "edit_transaction": "Edit Transaction", "error": "Error", "transaction_not_found": "Transaction not found", "validation_error": "Validation Error", "transaction_number_required": "Transaction number is required", "description_required": "Description is required", "details_required": "Transaction details are required", "debits_credits_must_equal": "Total debits must equal total credits", "save_error": "Error saving transaction", "customer": "Customer", "all_customers": "All Customers", "vendor": "<PERSON><PERSON><PERSON>", "all_vendors": "All Vendors", "invoice_number": "Invoice Number", "due_date": "Due Date", "paid": "Paid", "balance": "Balance", "total_receivable": "Total Receivable", "total_payable": "Total Payable", "export": "Export", "print": "Print", "period": "Period", "custom_period": "Custom Period", "current_month": "Current Month", "previous_month": "Previous Month", "current_quarter": "Current Quarter", "current_year": "Current Year", "previous_year": "Previous Year", "as_of_date": "As of Date", "generate_report": "Generate Report", "not_implemented": "Not Implemented", "feature_not_implemented": "This feature is not yet implemented", "none": "None", "invoice": "Invoice", "payment": "Payment", "expense": "Expense", "manual": "Manual", "add_entry": "Add Entry", "filter_transactions": "Filter Transactions", "apply_filters": "Apply Filters", "transaction_type": "Transaction Type", "all_types": "All Types", "start_date": "Start Date", "end_date": "End Date", "account_type": "Account Type", "transaction_list": "Transaction List", "transaction_details_tab": "Transaction Details", "account_tab": "Account", "details_tab": "Details", "mdn": "Debit", "dain": "Credit"}}