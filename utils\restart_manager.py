#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Restart Manager
Handles application restart functionality for settings that require restart.
"""

import sys
import os
import subprocess
import logging
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer
from utils.translation_manager import tr

logger = logging.getLogger(__name__)


class RestartManager:
    """Manages application restart functionality."""
    
    @staticmethod
    def restart_application():
        """Restart the application.
        
        Returns:
            bool: True if restart was initiated successfully, False otherwise
        """
        try:
            # Get the current executable path
            if getattr(sys, 'frozen', False):
                # Running as compiled executable
                executable = sys.executable
            else:
                # Running as Python script
                executable = sys.executable
                script_path = sys.argv[0]
                
                # Make sure we have the full path to the script
                if not os.path.isabs(script_path):
                    script_path = os.path.abspath(script_path)
            
            # Prepare the command to restart the application
            if getattr(sys, 'frozen', False):
                # For compiled executable
                cmd = [executable] + sys.argv[1:]
            else:
                # For Python script
                cmd = [executable, script_path] + sys.argv[1:]
            
            logger.info(f"Restarting application with command: {' '.join(cmd)}")
            
            # Start the new process
            if sys.platform.startswith('win'):
                # Windows
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                # Unix-like systems
                subprocess.Popen(cmd)
            
            # Close the current application
            QApplication.quit()
            return True
            
        except Exception as e:
            logger.error(f"Error restarting application: {str(e)}")
            return False
    
    @staticmethod
    def show_restart_confirmation(parent, title, message, save_callback=None):
        """Show a confirmation dialog with restart option.
        
        Args:
            parent: Parent widget for the dialog
            title (str): Dialog title
            message (str): Dialog message
            save_callback (callable, optional): Function to call before restart to save settings
            
        Returns:
            bool: True if user chose to restart, False otherwise
        """
        try:
            # Create custom message box
            msg_box = QMessageBox(parent)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Information)
            
            # Add custom buttons
            ok_button = msg_box.addButton(tr("messages.ok", "موافق"), QMessageBox.AcceptRole)
            restart_button = msg_box.addButton(tr("messages.restart_now", "إعادة التشغيل الآن"), QMessageBox.ActionRole)
            
            # Set button styles
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
                QPushButton:pressed {
                    background-color: #545b62;
                }
            """)
            
            restart_button.setStyleSheet("""
                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #0b5ed7;
                }
                QPushButton:pressed {
                    background-color: #0a58ca;
                }
            """)
            
            # Show the dialog
            msg_box.exec()
            
            # Check which button was clicked
            clicked_button = msg_box.clickedButton()
            
            if clicked_button == restart_button:
                # User chose to restart
                if save_callback:
                    # Call the save callback first
                    try:
                        save_callback()
                    except Exception as e:
                        logger.error(f"Error in save callback: {str(e)}")
                        # Show error but continue with restart
                        QMessageBox.warning(
                            parent,
                            tr("messages.warning", "تحذير"),
                            tr("messages.save_error_before_restart", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}\n\nسيتم المتابعة مع إعادة التشغيل.")
                        )
                
                # Delay restart slightly to allow UI to update
                QTimer.singleShot(500, RestartManager.restart_application)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error showing restart confirmation: {str(e)}")
            return False
    
    @staticmethod
    def show_restart_required_message(parent, setting_name):
        """Show a simple message that restart is required for a setting.
        
        Args:
            parent: Parent widget for the dialog
            setting_name (str): Name of the setting that was changed
        """
        try:
            QMessageBox.information(
                parent,
                tr("messages.restart_required_title", "إعادة التشغيل مطلوبة"),
                tr("messages.restart_required_message", f"تم حفظ إعدادات {setting_name} بنجاح.\n\nسيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.")
            )
        except Exception as e:
            logger.error(f"Error showing restart required message: {str(e)}")


def create_enhanced_message_box(parent, title, message, save_callback=None):
    """Create an enhanced message box with restart option.
    
    This is a convenience function that wraps RestartManager.show_restart_confirmation.
    
    Args:
        parent: Parent widget for the dialog
        title (str): Dialog title
        message (str): Dialog message
        save_callback (callable, optional): Function to call before restart to save settings
        
    Returns:
        bool: True if user chose to restart, False otherwise
    """
    return RestartManager.show_restart_confirmation(parent, title, message, save_callback)
